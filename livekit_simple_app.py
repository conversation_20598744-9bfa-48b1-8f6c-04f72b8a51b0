import os
import streamlit as st
import jwt
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# LiveKit settings
LIVEKIT_API_KEY = os.getenv("LIVEKIT_API_KEY")
LIVEKIT_API_SECRET = os.getenv("LIVEKIT_API_SECRET")
LIVEKIT_URL = os.getenv("LIVEKIT_URL", "wss://magonia-z3n9n4xq.livekit.cloud")

def create_token(user_identity: str, room_name: str) -> str:
    """Create an access token for a user to join a room."""
    try:
        now = int(time.time())
        payload = {
            "iss": LIVEKIT_API_KEY,
            "sub": user_identity,
            "iat": now,
            "exp": now + 3600,  # Token expires in 1 hour
            "video": {
                "room": room_name,
                "roomJoin": True,
                "canPublish": True,
                "canSubscribe": True,
                "canPublishData": True
            }
        }
        token = jwt.encode(payload, LIVEKIT_API_SECRET, algorithm="HS256")
        return token
    except Exception as e:
        st.error(f"Error creating LiveKit token: {str(e)}")
        return ""

def main():
    st.set_page_config(
        page_title="Simple LiveKit Chat",
        page_icon="🎥",
        layout="wide"
    )
    
    st.title("🎥 Simple LiveKit Video Chat")
    
    # Check credentials
    if not LIVEKIT_API_KEY or not LIVEKIT_API_SECRET:
        st.error("❌ LiveKit credentials not configured!")
        st.info("Please add LIVEKIT_API_KEY and LIVEKIT_API_SECRET to your .env file")
        st.stop()
    
    # Input fields
    col1, col2 = st.columns(2)
    
    with col1:
        user_name = st.text_input("👤 Your Name", value="User")
    
    with col2:
        room_name = st.text_input("🏠 Room Name", value="test-room")
    
    if st.button("🚀 Join Video Chat", type="primary"):
        if user_name and room_name:
            token = create_token(user_name, room_name)
            
            if token:
                st.success(f"✅ Joining room: {room_name} as {user_name}")
                
                # Create simple HTML with direct script loading
                html_content = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>LiveKit Video Chat</title>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <script src="https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.umd.js"></script>
                    <style>
                        body {{
                            font-family: Arial, sans-serif;
                            padding: 20px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            margin: 0;
                        }}
                        .container {{
                            max-width: 800px;
                            margin: 0 auto;
                            background: rgba(255, 255, 255, 0.1);
                            padding: 20px;
                            border-radius: 15px;
                            backdrop-filter: blur(10px);
                        }}
                        .status {{
                            padding: 15px;
                            margin: 15px 0;
                            border-radius: 10px;
                            background: rgba(255, 255, 255, 0.2);
                            text-align: center;
                            font-weight: bold;
                        }}
                        .controls {{
                            text-align: center;
                            margin: 20px 0;
                        }}
                        .btn {{
                            padding: 10px 20px;
                            margin: 5px;
                            border: none;
                            border-radius: 25px;
                            cursor: pointer;
                            font-weight: bold;
                            color: white;
                        }}
                        .btn-primary {{ background: #4CAF50; }}
                        .btn-danger {{ background: #f44336; }}
                        .btn-secondary {{ background: #2196F3; }}
                        .btn:disabled {{ opacity: 0.6; cursor: not-allowed; }}
                        #video-container {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                            gap: 15px;
                            margin: 20px 0;
                        }}
                        .video-element {{
                            width: 100%;
                            height: 200px;
                            background: #000;
                            border-radius: 10px;
                            object-fit: cover;
                        }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h2>🎥 Video Chat: {room_name}</h2>
                        <p>User: {user_name}</p>
                        
                        <div class="status" id="status">Initializing...</div>
                        
                        <div class="controls">
                            <button class="btn btn-primary" id="connect-btn" onclick="connectToRoom()">Connect</button>
                            <button class="btn btn-danger" id="disconnect-btn" onclick="disconnectFromRoom()" disabled>Disconnect</button>
                            <button class="btn btn-secondary" id="toggle-video" onclick="toggleVideo()" disabled>Toggle Video</button>
                            <button class="btn btn-secondary" id="toggle-audio" onclick="toggleAudio()" disabled>Toggle Audio</button>
                        </div>
                        
                        <div id="video-container"></div>
                    </div>
                    
                    <script>
                        const token = '{token}';
                        const wsUrl = '{LIVEKIT_URL}';
                        const roomName = '{room_name}';
                        const userName = '{user_name}';
                        
                        let room = null;
                        let localVideoTrack = null;
                        let localAudioTrack = null;
                        
                        const statusEl = document.getElementById('status');
                        const connectBtn = document.getElementById('connect-btn');
                        const disconnectBtn = document.getElementById('disconnect-btn');
                        const toggleVideoBtn = document.getElementById('toggle-video');
                        const toggleAudioBtn = document.getElementById('toggle-audio');
                        const videoContainer = document.getElementById('video-container');
                        
                        // Wait for LiveKit to load
                        function checkLiveKit() {{
                            if (typeof LiveKit !== 'undefined') {{
                                statusEl.textContent = '✅ Ready to connect!';
                                statusEl.style.background = 'rgba(76, 175, 80, 0.3)';
                                connectBtn.disabled = false;
                            }} else {{
                                statusEl.textContent = '🔄 Loading LiveKit...';
                                setTimeout(checkLiveKit, 500);
                            }}
                        }}
                        
                        async function connectToRoom() {{
                            try {{
                                if (typeof LiveKit === 'undefined') {{
                                    statusEl.textContent = '❌ LiveKit not loaded';
                                    return;
                                }}
                                
                                statusEl.textContent = '🔄 Connecting...';
                                
                                room = new LiveKit.Room();
                                
                                room.on(LiveKit.RoomEvent.Connected, () => {{
                                    statusEl.textContent = '🎉 Connected!';
                                    connectBtn.disabled = true;
                                    disconnectBtn.disabled = false;
                                    toggleVideoBtn.disabled = false;
                                    toggleAudioBtn.disabled = false;
                                }});
                                
                                room.on(LiveKit.RoomEvent.Disconnected, () => {{
                                    statusEl.textContent = '📴 Disconnected';
                                    connectBtn.disabled = false;
                                    disconnectBtn.disabled = true;
                                    toggleVideoBtn.disabled = true;
                                    toggleAudioBtn.disabled = true;
                                    videoContainer.innerHTML = '';
                                }});
                                
                                room.on(LiveKit.RoomEvent.TrackSubscribed, (track, publication, participant) => {{
                                    if (track.kind === 'video') {{
                                        const videoElement = track.attach();
                                        videoElement.className = 'video-element';
                                        videoContainer.appendChild(videoElement);
                                    }}
                                }});
                                
                                await room.connect(wsUrl, token);
                                
                                // Enable camera and microphone
                                try {{
                                    localVideoTrack = await LiveKit.createLocalVideoTrack();
                                    localAudioTrack = await LiveKit.createLocalAudioTrack();
                                    
                                    await room.localParticipant.publishTrack(localVideoTrack);
                                    await room.localParticipant.publishTrack(localAudioTrack);
                                    
                                    // Add local video
                                    const videoElement = localVideoTrack.attach();
                                    videoElement.className = 'video-element';
                                    videoElement.muted = true;
                                    videoContainer.appendChild(videoElement);
                                }} catch (mediaError) {{
                                    console.warn('Media access denied:', mediaError);
                                }}
                                
                            }} catch (error) {{
                                statusEl.textContent = `❌ Connection failed: ${{error.message}}`;
                                console.error('Connection failed:', error);
                            }}
                        }}
                        
                        async function disconnectFromRoom() {{
                            if (room) {{
                                await room.disconnect();
                                room = null;
                                localVideoTrack = null;
                                localAudioTrack = null;
                            }}
                        }}
                        
                        async function toggleVideo() {{
                            if (localVideoTrack) {{
                                if (localVideoTrack.isMuted) {{
                                    await localVideoTrack.unmute();
                                    toggleVideoBtn.textContent = 'Turn Off Video';
                                }} else {{
                                    await localVideoTrack.mute();
                                    toggleVideoBtn.textContent = 'Turn On Video';
                                }}
                            }}
                        }}
                        
                        async function toggleAudio() {{
                            if (localAudioTrack) {{
                                if (localAudioTrack.isMuted) {{
                                    await localAudioTrack.unmute();
                                    toggleAudioBtn.textContent = 'Mute Audio';
                                }} else {{
                                    await localAudioTrack.mute();
                                    toggleAudioBtn.textContent = 'Unmute Audio';
                                }}
                            }}
                        }}
                        
                        // Start checking for LiveKit
                        checkLiveKit();
                    </script>
                </body>
                </html>
                """
                
                st.components.v1.html(html_content, height=600)
        else:
            st.error("Please enter both name and room name")

if __name__ == "__main__":
    main()
