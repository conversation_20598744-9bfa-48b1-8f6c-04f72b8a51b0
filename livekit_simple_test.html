<!DOCTYPE html>
<html>
<head>
    <title>LiveKit Simple Test</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f2f6;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>LiveKit Client Loading Test</h1>
    <div id="status" class="status loading">🔄 Loading LiveKit client...</div>
    <div id="details"></div>
    
    <script>
        const statusEl = document.getElementById('status');
        const detailsEl = document.getElementById('details');
        
        // Try multiple CDN sources
        const cdnSources = [
            'https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.umd.js',
            'https://cdn.jsdelivr.net/npm/livekit-client@2.5.7/dist/livekit-client.umd.js',
            'https://unpkg.com/livekit-client@latest/dist/livekit-client.umd.js'
        ];
        
        let currentIndex = 0;
        
        function loadScript() {
            if (currentIndex >= cdnSources.length) {
                statusEl.textContent = '❌ Failed to load LiveKit from all sources';
                statusEl.className = 'status error';
                detailsEl.innerHTML = '<p>All CDN sources failed. Please check your internet connection.</p>';
                return;
            }
            
            const script = document.createElement('script');
            script.src = cdnSources[currentIndex];
            
            script.onload = function() {
                if (typeof LiveKit !== 'undefined') {
                    statusEl.textContent = '✅ LiveKit client loaded successfully!';
                    statusEl.className = 'status success';
                    detailsEl.innerHTML = `
                        <p><strong>Source:</strong> ${cdnSources[currentIndex]}</p>
                        <p><strong>LiveKit version:</strong> ${LiveKit.version || 'Unknown'}</p>
                        <p><strong>Available classes:</strong> ${Object.keys(LiveKit).slice(0, 10).join(', ')}...</p>
                    `;
                    
                    // Test creating a room
                    try {
                        const room = new LiveKit.Room();
                        detailsEl.innerHTML += '<p><strong>Room creation test:</strong> ✅ Passed</p>';
                    } catch (error) {
                        detailsEl.innerHTML += `<p><strong>Room creation test:</strong> ❌ Failed - ${error.message}</p>`;
                    }
                } else {
                    statusEl.textContent = '⚠️ Script loaded but LiveKit is undefined';
                    statusEl.className = 'status error';
                    currentIndex++;
                    setTimeout(loadScript, 500);
                }
            };
            
            script.onerror = function() {
                console.warn(`Failed to load from: ${cdnSources[currentIndex]}`);
                currentIndex++;
                setTimeout(loadScript, 500);
            };
            
            document.head.appendChild(script);
        }
        
        // Start loading
        loadScript();
    </script>
</body>
</html>
