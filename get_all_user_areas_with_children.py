from typing import Optional, Dict, Any
from flask import g
from langchain_core.tools import tool
import logging

logger = logging.getLogger(__name__)

@tool
def get_all_user_areas_with_children(tool_input: Optional[str] = None) -> dict:
    """
    Retrieve a complete hierarchical list of all the user's fields, areas, and their sub-areas.

    USE THIS TOOL WHEN:
    - The user asks for a list of all their fields or areas
    - The user wants to know what fields they have
    - The user needs an overview of their farm structure
    - The user asks about the organization of their agricultural areas
    - The user asks "quelles sont mes parcelles ?" or similar questions about their fields
    - The user wants to see all their agricultural areas
    - The user needs a complete list of their fields

    DO NOT USE THIS TOOL WHEN:
    - The user asks about a specific field's details (use field-specific tools)
    - The user asks about irrigation or soil moisture (use specialized tools)
    - The user asks about historical data for fields
    - The user is asking about someone else's fields

    EXAMPLE QUERIES:
    - "What fields do I have?"
    - "List all my agricultural areas"
    - "Show me all my fields and their organization"
    - "What are all the areas I'm managing?"
    - "Quelles sont mes parcelles ?"
    - "Montrez-moi toutes mes parcelles"
    - "Liste de mes champs"

    Args:
        tool_input (str, optional): Ignored parameter, exists for compatibility.

    Returns:
        dict: A hierarchical summary of all user areas with their child areas,
              including names and identifiers, or an error message if data retrieval fails.
    """
    try:
        logger.info("Retrieving all user areas and their children")
        response = g.seabex_api.tools().areas().call_tool(
            'get_all_user_areas_with_children'
        )

        # Check if response is valid
        if not response:
            logger.warning("Empty response from API")
            return {
                "error": "no_data",
                "message": "No fields found in your account.",
                "details": [
                    "You may need to add fields to your account",
                    "Check if you have the correct permissions",
                    "Verify your account settings"
                ],
                "suggestions": [
                    "Add new fields through the platform interface",
                    "Contact support if you believe this is an error",
                    "Check your account settings"
                ]
            }

        # Format the response for better readability
        formatted_response = {
            "areas": [],
            "total_areas": 0,
            "total_fields": 0
        }

        # Process all areas
        for area in response:
            area_info = {
                "name": area.get("area_name", "Unknown"),
                "size": float(area.get("field_size", 0)),
                "position": area.get("position", {}),
                "children": []
            }
            
            # Process children if any
            children = area.get("children", [])
            if children:
                for child in children:
                    child_info = {
                        "name": child.get("child_name", "Unknown"),
                        "size": float(child.get("field_size", 0)),
                        "position": child.get("position", {})
                    }
                    area_info["children"].append(child_info)
                    formatted_response["total_fields"] += 1
            
            formatted_response["areas"].append(area_info)
            formatted_response["total_areas"] += 1

        # Add summary
        formatted_response["summary"] = (
            f"Found {formatted_response['total_areas']} area(s) "
            f"with {formatted_response['total_fields']} field(s) in total."
        )

        logger.info(f"Successfully retrieved {formatted_response['total_areas']} areas with {formatted_response['total_fields']} fields")
        return formatted_response

    except Exception as e:
        logger.error(f"Error retrieving user areas: {str(e)}", exc_info=True)
        return {
            "error": "system_error",
            "message": "I'm sorry, I couldn't retrieve your fields at the moment.",
            "details": "Please try again later or contact support if the issue persists."
        }
