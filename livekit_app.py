import os
import streamlit as st
import jwt
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# LiveKit settings
LIVEKIT_API_KEY = os.getenv("LIVEKIT_API_KEY")
LIVEKIT_API_SECRET = os.getenv("LIVEKIT_API_SECRET")
LIVEKIT_URL = os.getenv("LIVEKIT_URL", "wss://magonia-z3n9n4xq.livekit.cloud")

class LiveKitManager:
    def __init__(self, api_key: str, api_secret: str, url: str):
        self.api_key = api_key
        self.api_secret = api_secret
        self.url = url
        
    def create_token(self, user_identity: str, room_name: str, permissions: dict = None) -> str:
        """Create an access token for a user to join a room."""
        try:
            now = int(time.time())
            
            # Default permissions
            default_permissions = {
                "roomJoin": True,
                "canPublish": True,
                "canSubscribe": True,
                "canPublishData": True
            }
            
            if permissions:
                default_permissions.update(permissions)
            
            payload = {
                "iss": self.api_key,
                "sub": user_identity,
                "iat": now,
                "exp": now + 3600,  # Token expires in 1 hour
                "video": {
                    "room": room_name,
                    **default_permissions
                }
            }
            
            token = jwt.encode(payload, self.api_secret, algorithm="HS256")
            return token
        except Exception as e:
            st.error(f"Error creating LiveKit token: {str(e)}")
            return ""

def create_video_chat_component(token: str, room_name: str, user_name: str, livekit_url: str):
    """Create the video chat HTML component."""
    
    video_chat_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>LiveKit Video Chat</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.umd.js" 
                onerror="this.onerror=null; this.src='https://cdn.jsdelivr.net/npm/livekit-client@2.5.7/dist/livekit-client.umd.js'"></script>
        <style>
            body {{
                margin: 0;
                padding: 20px;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
            }}
            
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }}
            
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            
            .room-info {{
                background: rgba(255, 255, 255, 0.2);
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 20px;
                text-align: center;
            }}
            
            .status {{
                text-align: center;
                padding: 15px;
                margin: 15px 0;
                border-radius: 10px;
                background: rgba(255, 255, 255, 0.2);
                font-weight: bold;
            }}
            
            .controls {{
                text-align: center;
                margin: 20px 0;
                display: flex;
                justify-content: center;
                gap: 10px;
                flex-wrap: wrap;
            }}
            
            .btn {{
                padding: 12px 24px;
                border: none;
                border-radius: 25px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                transition: all 0.3s ease;
                min-width: 120px;
            }}
            
            .btn:hover {{
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }}
            
            .btn-primary {{ 
                background: linear-gradient(45deg, #4CAF50, #45a049);
                color: white; 
            }}
            
            .btn-danger {{ 
                background: linear-gradient(45deg, #f44336, #da190b);
                color: white; 
            }}
            
            .btn-secondary {{ 
                background: linear-gradient(45deg, #2196F3, #0b7dda);
                color: white; 
            }}
            
            .btn:disabled {{
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }}
            
            #video-container {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 15px;
                margin: 20px 0;
                min-height: 200px;
            }}
            
            .video-wrapper {{
                position: relative;
                background: rgba(0, 0, 0, 0.3);
                border-radius: 15px;
                overflow: hidden;
                aspect-ratio: 16/9;
            }}
            
            .video-element {{
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 15px;
            }}
            
            .video-label {{
                position: absolute;
                bottom: 10px;
                left: 10px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 12px;
            }}
            
            .participants {{
                background: rgba(255, 255, 255, 0.1);
                padding: 15px;
                border-radius: 10px;
                margin-top: 20px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎥 LiveKit Video Chat</h1>
            </div>
            
            <div class="room-info">
                <h3>Room: {room_name}</h3>
                <p>User: {user_name}</p>
            </div>
            
            <div class="status" id="status">Initializing...</div>
            
            <div class="controls">
                <button class="btn btn-primary" id="connect-btn">🔗 Connect</button>
                <button class="btn btn-danger" id="disconnect-btn" disabled>❌ Disconnect</button>
                <button class="btn btn-secondary" id="toggle-video" disabled>📹 Toggle Video</button>
                <button class="btn btn-secondary" id="toggle-audio" disabled>🎤 Toggle Audio</button>
                <button class="btn btn-secondary" id="share-screen" disabled>🖥️ Share Screen</button>
            </div>
            
            <div id="video-container"></div>
            
            <div class="participants">
                <h4>Participants: <span id="participant-count">0</span></h4>
                <div id="participant-list"></div>
            </div>
        </div>
        
        <script>
            window.addEventListener('load', function() {{
                if (typeof LiveKit === 'undefined') {{
                    document.getElementById('status').textContent = '❌ Error: LiveKit client not loaded. Please refresh the page.';
                    document.getElementById('status').style.background = 'rgba(244, 67, 54, 0.3)';
                    return;
                }}
                
                const token = '{token}';
                const wsUrl = '{livekit_url}';
                const roomName = '{room_name}';
                const userName = '{user_name}';
                
                let room = null;
                let localVideoTrack = null;
                let localAudioTrack = null;
                let isScreenSharing = false;
                
                const statusEl = document.getElementById('status');
                const connectBtn = document.getElementById('connect-btn');
                const disconnectBtn = document.getElementById('disconnect-btn');
                const toggleVideoBtn = document.getElementById('toggle-video');
                const toggleAudioBtn = document.getElementById('toggle-audio');
                const shareScreenBtn = document.getElementById('share-screen');
                const videoContainer = document.getElementById('video-container');
                const participantCount = document.getElementById('participant-count');
                const participantList = document.getElementById('participant-list');
                
                statusEl.textContent = '✅ Ready to connect. Click Connect to join the room.';
                statusEl.style.background = 'rgba(76, 175, 80, 0.3)';
                
                connectBtn.addEventListener('click', connectToRoom);
                disconnectBtn.addEventListener('click', disconnectFromRoom);
                toggleVideoBtn.addEventListener('click', toggleVideo);
                toggleAudioBtn.addEventListener('click', toggleAudio);
                shareScreenBtn.addEventListener('click', toggleScreenShare);
                
                async function connectToRoom() {{
                    try {{
                        statusEl.textContent = '🔄 Connecting...';
                        statusEl.style.background = 'rgba(255, 193, 7, 0.3)';
                        
                        room = new LiveKit.Room();
                        
                        // Set up event listeners
                        room.on(LiveKit.RoomEvent.TrackSubscribed, handleTrackSubscribed);
                        room.on(LiveKit.RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed);
                        room.on(LiveKit.RoomEvent.ParticipantConnected, updateParticipants);
                        room.on(LiveKit.RoomEvent.ParticipantDisconnected, updateParticipants);
                        
                        room.on(LiveKit.RoomEvent.Connected, () => {{
                            statusEl.textContent = `🎉 Connected to room: ${{roomName}}`;
                            statusEl.style.background = 'rgba(76, 175, 80, 0.3)';
                            connectBtn.disabled = true;
                            disconnectBtn.disabled = false;
                            toggleVideoBtn.disabled = false;
                            toggleAudioBtn.disabled = false;
                            shareScreenBtn.disabled = false;
                            updateParticipants();
                        }});
                        
                        room.on(LiveKit.RoomEvent.Disconnected, () => {{
                            statusEl.textContent = '📴 Disconnected from room';
                            statusEl.style.background = 'rgba(158, 158, 158, 0.3)';
                            connectBtn.disabled = false;
                            disconnectBtn.disabled = true;
                            toggleVideoBtn.disabled = true;
                            toggleAudioBtn.disabled = true;
                            shareScreenBtn.disabled = true;
                            videoContainer.innerHTML = '';
                            updateParticipants();
                        }});
                        
                        await room.connect(wsUrl, token);
                        
                        // Enable camera and microphone
                        try {{
                            localVideoTrack = await LiveKit.createLocalVideoTrack();
                            localAudioTrack = await LiveKit.createLocalAudioTrack();
                            
                            await room.localParticipant.publishTrack(localVideoTrack);
                            await room.localParticipant.publishTrack(localAudioTrack);
                            
                            // Add local video
                            addVideoElement(localVideoTrack, userName + ' (You)', true);
                        }} catch (mediaError) {{
                            console.warn('Could not access camera/microphone:', mediaError);
                            statusEl.textContent += ' (Camera/microphone access denied)';
                        }}
                        
                    }} catch (error) {{
                        statusEl.textContent = `❌ Connection failed: ${{error.message}}`;
                        statusEl.style.background = 'rgba(244, 67, 54, 0.3)';
                        console.error('Connection failed:', error);
                    }}
                }}
                
                async function disconnectFromRoom() {{
                    if (room) {{
                        await room.disconnect();
                        room = null;
                        localVideoTrack = null;
                        localAudioTrack = null;
                        isScreenSharing = false;
                    }}
                }}
                
                async function toggleVideo() {{
                    if (localVideoTrack) {{
                        if (localVideoTrack.isMuted) {{
                            await localVideoTrack.unmute();
                            toggleVideoBtn.textContent = '📹 Turn Off Video';
                        }} else {{
                            await localVideoTrack.mute();
                            toggleVideoBtn.textContent = '📹 Turn On Video';
                        }}
                    }}
                }}
                
                async function toggleAudio() {{
                    if (localAudioTrack) {{
                        if (localAudioTrack.isMuted) {{
                            await localAudioTrack.unmute();
                            toggleAudioBtn.textContent = '🎤 Mute Audio';
                        }} else {{
                            await localAudioTrack.mute();
                            toggleAudioBtn.textContent = '🎤 Unmute Audio';
                        }}
                    }}
                }}
                
                async function toggleScreenShare() {{
                    if (!room) return;
                    
                    try {{
                        if (!isScreenSharing) {{
                            const screenTrack = await LiveKit.createLocalScreenTrack();
                            await room.localParticipant.publishTrack(screenTrack);
                            addVideoElement(screenTrack, userName + ' (Screen)', false);
                            shareScreenBtn.textContent = '🖥️ Stop Sharing';
                            isScreenSharing = true;
                        }} else {{
                            // Stop screen sharing
                            const screenPublication = Array.from(room.localParticipant.trackPublications.values())
                                .find(pub => pub.source === LiveKit.Track.Source.ScreenShare);
                            if (screenPublication) {{
                                await room.localParticipant.unpublishTrack(screenPublication.track);
                            }}
                            shareScreenBtn.textContent = '🖥️ Share Screen';
                            isScreenSharing = false;
                        }}
                    }} catch (error) {{
                        console.error('Screen sharing error:', error);
                        statusEl.textContent = 'Screen sharing not supported or denied';
                    }}
                }}
                
                function addVideoElement(track, participantName, isLocal = false) {{
                    const wrapper = document.createElement('div');
                    wrapper.className = 'video-wrapper';
                    wrapper.id = `video-${{track.sid}}`;
                    
                    const videoElement = track.attach();
                    videoElement.className = 'video-element';
                    if (isLocal) {{
                        videoElement.muted = true; // Mute local video to avoid feedback
                    }}
                    
                    const label = document.createElement('div');
                    label.className = 'video-label';
                    label.textContent = participantName;
                    
                    wrapper.appendChild(videoElement);
                    wrapper.appendChild(label);
                    videoContainer.appendChild(wrapper);
                }}
                
                function handleTrackSubscribed(track, publication, participant) {{
                    if (track.kind === 'video') {{
                        addVideoElement(track, participant.identity);
                    }}
                }}
                
                function handleTrackUnsubscribed(track, publication, participant) {{
                    const videoWrapper = document.getElementById(`video-${{track.sid}}`);
                    if (videoWrapper) {{
                        videoWrapper.remove();
                    }}
                }}
                
                function updateParticipants() {{
                    if (!room) {{
                        participantCount.textContent = '0';
                        participantList.innerHTML = '';
                        return;
                    }}
                    
                    const participants = Array.from(room.participants.values());
                    participants.push(room.localParticipant); // Include local participant
                    
                    participantCount.textContent = participants.length;
                    
                    participantList.innerHTML = participants
                        .map(p => `<span style="margin-right: 10px;">👤 ${{p.identity}}</span>`)
                        .join('');
                }}
            }});
        </script>
    </body>
    </html>
    """
    
    return video_chat_html

def main():
    st.set_page_config(
        page_title="LiveKit Video Chat App",
        page_icon="🎥",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    st.title("🎥 LiveKit Video Chat Application")
    st.markdown("---")
    
    # Check if LiveKit credentials are configured
    if not LIVEKIT_API_KEY or not LIVEKIT_API_SECRET:
        st.error("❌ LiveKit credentials not configured!")
        st.info("Please add LIVEKIT_API_KEY and LIVEKIT_API_SECRET to your .env file")
        st.stop()
    
    # Initialize LiveKit manager
    if 'livekit_manager' not in st.session_state:
        st.session_state.livekit_manager = LiveKitManager(
            api_key=LIVEKIT_API_KEY,
            api_secret=LIVEKIT_API_SECRET,
            url=LIVEKIT_URL
        )
    
    # Sidebar configuration
    st.sidebar.header("🎬 Room Configuration")
    
    # Handle demo room and random room clicks
    default_user_name = 'User'
    default_room_name = 'general'

    if st.session_state.get('demo_room_clicked', False):
        default_user_name = 'Demo User'
        default_room_name = 'demo-room'
        st.session_state['demo_room_clicked'] = False

    if st.session_state.get('random_room_clicked', False):
        default_room_name = st.session_state.get('random_room_id', 'general')
        st.session_state['random_room_clicked'] = False

    # User settings
    user_name = st.sidebar.text_input(
        "👤 Your Name",
        value=default_user_name,
        key="user_name_input"
    )

    room_name = st.sidebar.text_input(
        "🏠 Room Name",
        value=default_room_name,
        key="room_name_input"
    )
    
    # Advanced settings
    st.sidebar.subheader("⚙️ Advanced Settings")
    
    can_publish = st.sidebar.checkbox("📹 Can Publish Video/Audio", value=True)
    can_subscribe = st.sidebar.checkbox("👀 Can Subscribe to Others", value=True)
    can_publish_data = st.sidebar.checkbox("💬 Can Send Data Messages", value=True)
    
    permissions = {
        "canPublish": can_publish,
        "canSubscribe": can_subscribe,
        "canPublishData": can_publish_data
    }
    
    # Generate token button
    if st.sidebar.button("🎫 Generate Access Token", type="primary"):
        if user_name and room_name:
            token = st.session_state.livekit_manager.create_token(
                user_identity=user_name,
                room_name=room_name,
                permissions=permissions
            )
            
            if token:
                st.session_state.access_token = token
                st.session_state.user_name = user_name
                st.session_state.room_name = room_name
                st.sidebar.success("✅ Token generated successfully!")
                st.sidebar.info(f"**Room:** {room_name}")
                st.sidebar.info(f"**User:** {user_name}")
        else:
            st.sidebar.error("Please enter both name and room name")
    
    # Display connection info
    if 'access_token' in st.session_state:
        st.sidebar.markdown("---")
        st.sidebar.success("🎉 Ready to join video chat!")
        
        if st.sidebar.button("🚀 Launch Video Chat", type="primary"):
            st.session_state.show_video_chat = True
    
    # Main content area
    if st.session_state.get('show_video_chat', False) and 'access_token' in st.session_state:
        st.markdown("### 🎥 Video Chat Room")
        
        # Create and display video chat component
        video_html = create_video_chat_component(
            token=st.session_state.access_token,
            room_name=st.session_state.room_name,
            user_name=st.session_state.user_name,
            livekit_url=LIVEKIT_URL
        )
        
        st.components.v1.html(video_html, height=800)
        
        # Control buttons
        col1, col2, col3 = st.columns([1, 1, 2])
        
        with col1:
            if st.button("🔄 Refresh Chat"):
                st.rerun()
        
        with col2:
            if st.button("❌ Close Chat"):
                st.session_state.show_video_chat = False
                st.rerun()
        
        with col3:
            st.info(f"**Room:** {st.session_state.room_name} | **User:** {st.session_state.user_name}")
    
    else:
        # Welcome screen
        st.markdown("""
        ## Welcome to LiveKit Video Chat! 🎉
        
        This application provides real-time video communication using LiveKit technology.
        
        ### 🚀 Getting Started:
        1. **Enter your name** in the sidebar
        2. **Choose a room name** (or use the default)
        3. **Configure permissions** if needed
        4. **Generate an access token**
        5. **Launch the video chat**
        
        ### ✨ Features:
        - 📹 **Video & Audio Chat** - High-quality real-time communication
        - 🖥️ **Screen Sharing** - Share your screen with participants
        - 👥 **Multiple Participants** - Support for multiple users in one room
        - 🎛️ **Media Controls** - Toggle video/audio on demand
        - 🔒 **Secure Access** - JWT-based authentication
        - 📱 **Responsive Design** - Works on desktop and mobile
        
        ### 🔧 Configuration:
        - **LiveKit Server:** `{}`
        - **API Status:** ✅ Connected
        """.format(LIVEKIT_URL))
        
        # Quick start section
        st.markdown("---")
        st.markdown("### 🎯 Quick Start")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🎮 Join Demo Room", type="primary"):
                st.session_state['demo_room_clicked'] = True
                st.rerun()

        with col2:
            if st.button("🎲 Random Room", type="secondary"):
                import random
                room_id = f"room-{random.randint(1000, 9999)}"
                st.session_state['random_room_id'] = room_id
                st.session_state['random_room_clicked'] = True
                st.rerun()

if __name__ == "__main__":
    main()
