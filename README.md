# 🌾 Magonia - Agricultural Assistant with Ollama

A Streamlit web application that provides an agricultural assistant powered by Ollama's devstral model. Magonia specializes in irrigation, crop management, and farming best practices.

## Features

- 🤖 **AI-Powered Assistant**: Uses <PERSON>llama's devstral model for intelligent responses
- 🌍 **Multi-language Support**: Responds in English, French, Spanish, and Tunisian Arabic
- 💬 **Interactive Chat Interface**: Clean and intuitive Streamlit web interface
- 🔧 **Configurable**: Easy to configure Ollama URL and model settings
- 📱 **Responsive Design**: Works on desktop and mobile devices

## Prerequisites

1. **Python 3.8+**: Make sure you have Python 3.8 or higher installed
2. **Ollama**: Install and run Ollama on your system
3. **devstral Model**: Install the devstral model in Ollama

## Quick Start

### Option 1: Automated Setup (Recommended)

Run the setup script that will handle everything for you:

```bash
python setup_and_run.py
```

This script will:
- Check your Python version
- Install required packages
- Verify <PERSON><PERSON><PERSON> is running
- Check for the devstral model
- Start the Streamlit application

### Option 2: Manual Setup

1. **Install Requirements**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Install and Start Ollama**:
   - Download from [https://ollama.ai](https://ollama.ai)
   - Install and start the Ollama service
   - Install the devstral model:
     ```bash
     ollama pull devstral
     ```

3. **Run the Application**:
   ```bash
   streamlit run streamlit_ollama_app.py
   ```

## Usage

1. **Start the Application**: The app will open in your browser (usually at `http://localhost:8501`)

2. **Configure Settings** (if needed):
   - Use the sidebar to adjust Ollama URL or model name
   - Default settings work for standard Ollama installations

3. **Chat with Magonia**:
   - Type your agricultural questions in the chat input
   - Ask about irrigation, crop management, farming practices
   - Magonia will respond in the same language you use

## Example Questions

- "How much water does my field need today?"
- "¿Cuánta agua necesita mi campo hoy?" (Spanish)
- "Combien d'eau mon champ a-t-il besoin aujourd'hui?" (French)
- "What are the best irrigation practices for tomatoes?"
- "When should I irrigate my crops?"

## Configuration

### Ollama Settings

- **Default URL**: `http://localhost:11434`
- **Default Model**: `devstral`
- **Configurable**: Change these in the sidebar of the application

### Model Options

While the application is configured for devstral, you can use other Ollama models by:
1. Installing the model: `ollama pull <model-name>`
2. Changing the model name in the sidebar

## Troubleshooting

### Common Issues

1. **"Could not connect to Ollama"**:
   - Make sure Ollama is installed and running
   - Check if the URL is correct (default: `http://localhost:11434`)
   - Verify Ollama service is accessible

2. **"devstral model not found"**:
   - Install the model: `ollama pull devstral`
   - Or use a different available model

3. **Application won't start**:
   - Check Python version (3.8+ required)
   - Install requirements: `pip install -r requirements.txt`
   - Verify Streamlit is installed: `pip install streamlit`

### Checking Ollama Status

```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# List available models
ollama list

# Pull devstral model
ollama pull devstral
```

## Files Structure

```
.
├── streamlit_ollama_app.py    # Main Streamlit application
├── setup_and_run.py          # Automated setup script
├── requirements.txt          # Python dependencies
├── README.md                # This file
└── gpt4o_direct.py          # Original GPT-4o implementation (reference)
```

## System Prompt

The application uses the same comprehensive system prompt as the original GPT-4o implementation, ensuring Magonia:
- Focuses on agricultural topics
- Provides helpful farming advice
- Maintains conversational and friendly tone
- Supports multiple languages
- Follows agricultural best practices

## Contributing

Feel free to contribute by:
- Reporting bugs
- Suggesting new features
- Improving documentation
- Adding support for more models

## License

This project is open source and available under the MIT License.

---

**Happy Farming! 🌾**
