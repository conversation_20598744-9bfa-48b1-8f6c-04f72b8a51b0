<!DOCTYPE html>
<html>
<head>
    <title>LiveKit Test</title>
    <script src="https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.umd.js" 
            onerror="this.onerror=null; this.src='https://cdn.jsdelivr.net/npm/livekit-client@2.5.7/dist/livekit-client.umd.js'"></script>
</head>
<body>
    <h1>LiveKit Client Test</h1>
    <div id="status">Loading...</div>
    
    <script>
        window.addEventListener('load', function() {
            const statusEl = document.getElementById('status');
            
            if (typeof LiveKit === 'undefined') {
                statusEl.textContent = 'Error: LiveKit client not loaded';
                statusEl.style.color = 'red';
            } else {
                statusEl.textContent = 'Success: LiveKit client loaded successfully!';
                statusEl.style.color = 'green';
                
                // Test creating a room instance
                try {
                    const room = new LiveKit.Room();
                    statusEl.textContent += ' Room creation test passed.';
                } catch (error) {
                    statusEl.textContent += ' Room creation test failed: ' + error.message;
                    statusEl.style.color = 'orange';
                }
            }
        });
    </script>
</body>
</html>
