#!/usr/bin/env python3
"""
Test script for unsloth/orpheus-3b-0.1-ft TTS model
"""

import torch
import time

def test_orpheus_model():
    """Test the Orpheus model for TTS enhancement"""
    print("🤖 Testing unsloth/orpheus-3b-0.1-ft model...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {device}")
        
        print("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained("unsloth/orpheus-3b-0.1-ft")
        
        print("Loading model...")
        model = AutoModelForCausalLM.from_pretrained(
            "unsloth/orpheus-3b-0.1-ft",
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            device_map="auto" if device == "cuda" else None,
            trust_remote_code=True
        )
        
        # Set pad token if not exists
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        print("✅ Model loaded successfully!")
        
        # Test TTS enhancement
        test_texts = [
            "Hello, this is a test of the text-to-speech system.",
            "Do I need to irrigate my field today?",
            "What are my registered farming areas?"
        ]
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n🧪 Test {i}: {text}")
            
            # Create TTS-focused prompt
            tts_prompt = f"""<|im_start|>system
You are a text-to-speech assistant. Convert the following text into natural, expressive speech patterns. Focus on clarity, proper pronunciation, and natural flow.
<|im_end|>
<|im_start|>user
Convert this text to speech: {text}
<|im_end|>
<|im_start|>assistant
"""
            
            # Tokenize
            inputs = tokenizer(
                tts_prompt, 
                return_tensors="pt", 
                truncation=True, 
                max_length=512,
                padding=True
            )
            
            if device == "cuda":
                inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # Generate
            print("Generating enhanced text...")
            start_time = time.time()
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=256,
                    temperature=0.7,
                    do_sample=True,
                    top_p=0.9,
                    repetition_penalty=1.1,
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id
                )
            
            generation_time = time.time() - start_time
            
            # Decode response
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract enhanced text
            if "<|im_start|>assistant" in response:
                enhanced_text = response.split("<|im_start|>assistant")[-1].strip()
                print(f"✅ Enhanced text: {enhanced_text[:200]}...")
                print(f"⏱️ Generation time: {generation_time:.2f}s")
            else:
                print("⚠️ Could not extract enhanced text")
                print(f"Raw response: {response[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Orpheus test failed: {e}")
        return False

def test_system_info():
    """Display system information"""
    print("🔧 System Information:")
    
    if torch.cuda.is_available():
        print(f"✅ CUDA: {torch.cuda.get_device_name()}")
        print(f"   Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        print(f"   CUDA version: {torch.version.cuda}")
    else:
        print("⚠️ CUDA not available")
    
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"💾 RAM: {memory.total / 1e9:.1f} GB total, {memory.available / 1e9:.1f} GB available")
    except ImportError:
        print("⚠️ psutil not available")

def main():
    print("🚀 Orpheus TTS Model Test")
    print("=" * 50)
    
    test_system_info()
    print()
    
    success = test_orpheus_model()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Orpheus model test completed successfully!")
        print("The model is ready for TTS enhancement in the main application.")
    else:
        print("❌ Orpheus model test failed.")
        print("Check the error messages above and ensure:")
        print("- Sufficient GPU memory (recommended: 6GB+ VRAM)")
        print("- Stable internet connection for model download")
        print("- Latest transformers library")

if __name__ == "__main__":
    main()
