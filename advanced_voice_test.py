#!/usr/bin/env python3
"""
Test script for advanced voice models: Whisper Large V3 and unsloth/csm-1b
"""

import os
import time
import torch
import numpy as np

def test_whisper():
    """Test Whisper Large V3 model"""
    print("\n🎤 Testing Whisper Large V3...")
    
    try:
        import whisper
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {device}")
        
        print("Loading Whisper Large V3 model...")
        model = whisper.load_model("large-v3", device=device)
        print("✅ Whisper Large V3 loaded successfully!")
        
        # Test with a sample audio file (if available)
        # For now, just confirm the model is loaded
        print(f"Model device: {next(model.parameters()).device}")
        print(f"Model dtype: {next(model.parameters()).dtype}")
        
        return True
        
    except Exception as e:
        print(f"❌ Whisper test failed: {e}")
        return False

def test_unsloth():
    """Test unsloth/csm-1b model"""
    print("\n🤖 Testing unsloth/csm-1b model...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {device}")
        
        print("Loading unsloth/csm-1b tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained("unsloth/csm-1b")
        print("✅ Tokenizer loaded successfully!")
        
        print("Loading unsloth/csm-1b model...")
        model = AutoModelForCausalLM.from_pretrained(
            "unsloth/csm-1b",
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            device_map="auto" if device == "cuda" else None
        )
        print("✅ Model loaded successfully!")
        
        # Test text generation
        test_text = "Convert the following text to speech: Hello, this is a test of the unsloth model."
        print(f"\nTesting with input: {test_text}")
        
        inputs = tokenizer(test_text, return_tensors="pt")
        if device == "cuda":
            inputs = {k: v.to(device) for k, v in inputs.items()}
        
        print("Generating response...")
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=100,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"✅ Generation successful!")
        print(f"Response: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ unsloth test failed: {e}")
        return False

def test_system_requirements():
    """Test system requirements"""
    print("🔧 Testing System Requirements...")
    
    # Check CUDA availability
    if torch.cuda.is_available():
        print(f"✅ CUDA available: {torch.cuda.get_device_name()}")
        print(f"   CUDA version: {torch.version.cuda}")
        print(f"   GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("⚠️ CUDA not available, using CPU")
    
    # Check available memory
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"💾 System RAM: {memory.total / 1e9:.1f} GB")
        print(f"   Available: {memory.available / 1e9:.1f} GB")
    except ImportError:
        print("⚠️ psutil not available, cannot check memory")
    
    # Check disk space
    try:
        import shutil
        disk_usage = shutil.disk_usage(".")
        print(f"💽 Disk space: {disk_usage.free / 1e9:.1f} GB free")
    except:
        print("⚠️ Cannot check disk space")

def main():
    print("🚀 Advanced Voice Models Test")
    print("=" * 50)
    
    # Test system requirements
    test_system_requirements()
    
    # Test models
    whisper_ok = test_whisper()
    unsloth_ok = test_unsloth()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Whisper Large V3: {'✅ PASS' if whisper_ok else '❌ FAIL'}")
    print(f"unsloth/csm-1b: {'✅ PASS' if unsloth_ok else '❌ FAIL'}")
    
    if whisper_ok and unsloth_ok:
        print("\n🎉 All advanced voice models are working!")
        print("You can now use the advanced voice features in the main application.")
    else:
        print("\n⚠️ Some models failed to load.")
        print("Check the error messages above and ensure you have:")
        print("- Sufficient GPU memory (recommended: 8GB+ VRAM)")
        print("- Stable internet connection for model downloads")
        print("- All required packages installed")

if __name__ == "__main__":
    main()
