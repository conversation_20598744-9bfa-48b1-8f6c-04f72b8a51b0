from typing import Optional

def lookup_document_tool(query: str) -> str:
    # Dummy implementation for demo
    if "irrigation" in query.lower():
        return "Irrigation is the process of applying water to crops to help them grow."
    return "I couldn't find specific documentation."

TOOLS = {
    "lookup_document_tool": lookup_document_tool,
    # Add more tools as needed
}

def call_tool_if_needed(user_message: str) -> Optional[str]:
    # Simple heuristic: call lookup_document_tool if "irrigation" is in the message
    if "irrigation" in user_message.lower():
        return TOOLS["lookup_document_tool"](user_message)
    return None
