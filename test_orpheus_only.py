#!/usr/bin/env python3
"""
Test script for Orpheus-only TTS setup
"""

import torch
import time
import tempfile
import os

def test_orpheus_tts():
    """Test Orpheus model for TTS generation"""
    print("🤖 Testing Orpheus-3B TTS (Speech Model)...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        from gtts import gTTS
        import pygame
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {device}")
        
        print("Loading Orpheus tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained("unsloth/orpheus-3b-0.1-ft")
        
        print("Loading Orpheus model...")
        model = AutoModelForCausalLM.from_pretrained(
            "unsloth/orpheus-3b-0.1-ft",
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            device_map="auto" if device == "cuda" else None,
            trust_remote_code=True
        )
        
        # Set pad token if not exists
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        print("✅ Orpheus model loaded successfully!")
        
        # Initialize pygame for audio
        pygame.mixer.init()
        
        # Test TTS generation
        test_texts = [
            "Hello, this is a test of Orpheus TTS.",
            "Your irrigation system is working properly.",
            "The weather forecast shows rain tomorrow."
        ]
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n🧪 Test {i}: {text}")
            
            # Create speech generation prompt
            speech_prompt = f"""<|im_start|>system
You are an advanced text-to-speech model. Generate natural, clear, and expressive speech from the given text. Focus on proper pronunciation, natural rhythm, and clarity.
<|im_end|>
<|im_start|>user
Generate speech for: {text}
<|im_end|>
<|im_start|>assistant
"""
            
            # Tokenize
            inputs = tokenizer(
                speech_prompt, 
                return_tensors="pt", 
                truncation=True, 
                max_length=512,
                padding=True
            )
            
            if device == "cuda":
                inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # Generate speech
            print("Generating speech with Orpheus...")
            start_time = time.time()
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=512,
                    temperature=0.8,
                    do_sample=True,
                    top_p=0.95,
                    top_k=50,
                    repetition_penalty=1.1,
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id
                )
            
            generation_time = time.time() - start_time
            
            # Decode response
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract speech content
            if "<|im_start|>assistant" in response:
                speech_content = response.split("<|im_start|>assistant")[-1].strip()
                
                if speech_content and len(speech_content) > 10:
                    final_text = speech_content[:300]
                    print(f"✅ Orpheus generated: {final_text[:100]}...")
                    print(f"⏱️ Generation time: {generation_time:.2f}s")
                    
                    # Convert to audio with gTTS
                    print("Converting to audio...")
                    try:
                        tts = gTTS(text=final_text, lang='en', slow=False)
                        
                        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_file:
                            temp_filename = temp_file.name
                            tts.save(temp_filename)
                        
                        print("🔊 Playing audio...")
                        pygame.mixer.music.load(temp_filename)
                        pygame.mixer.music.play()
                        
                        # Wait for playback
                        while pygame.mixer.music.get_busy():
                            time.sleep(0.1)
                        
                        # Clean up
                        os.remove(temp_filename)
                        print("✅ Audio playback completed!")
                        
                    except Exception as audio_error:
                        print(f"❌ Audio error: {audio_error}")
                else:
                    print("⚠️ Empty speech content generated")
            else:
                print("⚠️ Could not extract speech content")
                print(f"Raw response: {response[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Orpheus TTS test failed: {e}")
        return False

def main():
    print("🚀 Orpheus-Only TTS Test")
    print("=" * 50)
    
    # System info
    if torch.cuda.is_available():
        print(f"✅ CUDA: {torch.cuda.get_device_name()}")
        print(f"   Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("⚠️ Using CPU")
    
    print()
    success = test_orpheus_tts()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Orpheus-only TTS test completed successfully!")
        print("The setup is ready for speech generation in the main application.")
        print("\nKey features:")
        print("- ✅ Orpheus-3B for enhanced speech generation")
        print("- ✅ Google TTS for audio conversion")
        print("- ✅ Auto-speak responses enabled")
    else:
        print("❌ Orpheus TTS test failed.")
        print("Check the error messages above.")

if __name__ == "__main__":
    main()
