import os
import sys
import json
import re
import traceback
import requests
from datetime import datetime
from typing import List, Dict, Optional, Any
from dotenv import load_dotenv
from seabex_api import SeabexAPI
import streamlit as st
from livekit.rtc import RoomServiceClient, AccessToken, RoomJoin  # Updated LiveKit imports

# Load environment variables
load_dotenv()

# Seabex credentials (initial load, will be passed as parameter)
SEABEX_CLIENT_ID = os.getenv("SEABEX_CLIENT_ID")
SEABEX_CLIENT_SECRET = os.getenv("SEABEX_CLIENT_SECRET")
DEFAULT_SEABEX_USER_ID = os.getenv("USER_ID", "f68381cd-a748-47bd-842c-701790b35e3c")

# Ollama settings
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
OLLAMA_MODEL = os.getenv("OLLAMA_MODEL", "qwen3:14b")

# LiveKit settings
LIVEKIT_API_KEY = os.getenv("LIVEKIT_API_KEY")
LIVEKIT_API_SECRET = os.getenv("LIVEKIT_API_SECRET")
LIVEKIT_URL = os.getenv("LIVEKIT_URL", "wss://magonia-z3n9n4xq.livekit.cloud")

# Known field names (These might be dynamically updated by get_all_user_areas_with_children later)
FIELD_KEYWORDS = ["chlewi", "bir ali", "taba", "sidi salah", "metwiya", "menzel"]


# ---------- Ollama Client ----------
class OllamaClient:
    def __init__(self, base_url: str, model: str):
        self.base_url = base_url
        self.model = model

    def get_available_models(self) -> List[str]:
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                return [model['name'] for model in response.json().get('models', [])]
            return []
        except Exception as e:
            st.error(f"Error connecting to Ollama: {e}")
            return []

    def generate_response(self, messages: List[Dict[str, str]], stream: bool = False) -> str:
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": stream,
            "options": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 500}
        }
        try:
            response = requests.post(f"{self.base_url}/api/chat", json=payload)
            response.raise_for_status()
            return response.json()['message']['content']
        except Exception as e:
            return f"Error during Ollama call: {str(e)}"


# ---------- Seabex Tool Calls ----------

@st.cache_data(show_spinner=False) # Cache the results of this function
def get_water_amount_to_irrigate(field_name: str, date_of_calculation: str, user_id: str) -> Optional[float]:
    """
    Calls the Seabex API to get the recommended water amount for irrigation.
    """
    if not SEABEX_CLIENT_ID or not SEABEX_CLIENT_SECRET:
        st.error("Seabex API credentials (CLIENT_ID, CLIENT_SECRET) are not set. Please check your .env file or environment variables.")
        return None
    try:
        api = SeabexAPI(SEABEX_CLIENT_ID, SEABEX_CLIENT_SECRET)
        api.set_user_id(user_id)
        api.set_scopes(["magonia-api"])
        api.authenticate()

        api.tools().irrigations() # Assuming this sets up the correct tool context

        cache_buster = datetime.now().isoformat() + "_prxakslp"
        payload = {
            "user_id": user_id,
            "field_name": field_name,
            "date_of_calculation": date_of_calculation,
            "_cache_buster": cache_buster
        }

        st.info(f"🔗 Calling API: `check_irrigation_user_data` with payload: `{payload}`")
        result = api.call_tool("check_irrigation_user_data", payload)
        st.success(f"✅ API Response: `{result}`")

        if isinstance(result, dict):
            data = result.get("data")
        else:
            data = result

        try:
            water_mm = float(data)
            if water_mm == 0:
                st.info("💧 No irrigation is needed.")
                return 0.0
            st.info(f"💧 Irrigation needed: {water_mm} mm")
            return water_mm
        except (TypeError, ValueError):
            st.warning("⚠️ Data could not be converted to float.")
            return None

    except Exception as e:
        st.error(f"Error during Seabex API call for irrigation: {traceback.format_exc()}")
        return None

@st.cache_data(show_spinner=False) # Cache the results of this function
def get_all_user_areas_with_children(user_id: str) -> Optional[List[Dict[str, Any]]]:
    """
    Calls the Seabex API to get all user areas and their children (fields).
    """
    if not SEABEX_CLIENT_ID or not SEABEX_CLIENT_SECRET:
        st.error("Seabex API credentials (CLIENT_ID, CLIENT_SECRET) are not set. Please check your .env file or environment variables.")
        return None
    try:
        api = SeabexAPI(SEABEX_CLIENT_ID, SEABEX_CLIENT_SECRET)
        api.set_user_id(user_id)
        api.set_scopes(["magonia-api"])
        api.authenticate()

        # Assuming this tool exists and is accessible via api.tools().areas()
        api.tools().areas() 

        cache_buster = datetime.now().isoformat() + "_getareas"
        payload = {
            "user_id": user_id,
            "_cache_buster": cache_buster
        }

        st.info(f"🔗 Calling API: `get_all_user_areas_with_children` with payload: `{payload}`")
        result = api.call_tool("get_all_user_areas_with_children", payload)
        st.success(f"✅ API Response: `{result}`")

        # --- UPDATED LOGIC FOR PARSING API RESPONSE ---
        processed_areas = []
        if isinstance(result, list):
            raw_areas = result
        elif isinstance(result, dict) and "data" in result and isinstance(result["data"], list):
            raw_areas = result["data"]
        else:
            st.warning("⚠️ Unexpected data format from `get_all_user_areas_with_children`.")
            return None

        for area in raw_areas:
            area_info = {}
            area_info["area_name"] = area.get("area_name", "Unknown Area") # Use 'area_name' key
            
            children_list = []
            if "children" in area and isinstance(area["children"], list):
                for child in area["children"]:
                    if isinstance(child, dict):
                        # Use 'child_name' key for children/fields
                        children_list.append({"child_name": child.get("child_name", "Unknown Field")}) 
            area_info["children"] = children_list
            processed_areas.append(area_info)
        
        return processed_areas

    except Exception as e:
        st.error(f"Error during Seabex API call for user areas: {traceback.format_exc()}")
        return None


# ---------- LiveKit Client ----------
class LiveKitClient:
    def __init__(self, api_key: str, api_secret: str, url: str):
        self.api_key = api_key
        self.api_secret = api_secret
        self.url = url
        self.room_service = RoomServiceClient(url, api_key, api_secret)
        
    def create_token(self, user_identity: str, room_name: str) -> str:
        """Create an access token for a user to join a room."""
        try:
            token = AccessToken(self.api_key, self.api_secret)
            token.add_grant(RoomJoin(room=room_name))
            token.identity = user_identity
            return token.to_jwt()
        except Exception as e:
            st.error(f"Error creating LiveKit token: {str(e)}")
            return ""
            
    def create_room(self, room_name: str) -> bool:
        """Create a LiveKit room if it doesn't exist."""
        try:
            self.room_service.create_room(room_name)
            return True
        except Exception as e:
            st.error(f"Error creating LiveKit room: {str(e)}")
            return False


# ---------- Core Logic ----------
def process_user_query_with_tools(query: str, ollama_client: OllamaClient, seabex_user_id: str) -> str:
    query_lower = query.lower()
    tool_output = None
    llm_prompt = ""

    # Check for irrigation query
    if any(term in query_lower for term in ["irrigate", "irrigation", "ري"]):
        found_field = next((f for f in FIELD_KEYWORDS if re.search(rf'\b{re.escape(f)}\b', query_lower)), None)
        date_match = re.search(r"\d{4}-\d{2}-\d{2}", query)
        date = date_match.group(0) if date_match else datetime.now().strftime('%Y-%m-%d')

        if found_field:
            st.info(f"📍 Detected Field: **{found_field.capitalize()}** | 📅 Date: **{date}**")
            with st.spinner(f"Checking irrigation recommendations for {found_field.capitalize()}..."):
                water_amount = get_water_amount_to_irrigate(found_field, date, seabex_user_id) # Pass seabex_user_id
            
            if water_amount is None:
                tool_output = f"Irrigation is recommended for your field **{found_field.capitalize()}** on **{date}**, but the system did not provide a specific water amount."
            elif water_amount == 0:
                tool_output = f"No irrigation is needed for your field **{found_field.capitalize()}** on **{date}**."
            else:
                tool_output = f"Yes, irrigation is recommended for your field **{found_field.capitalize()}** on **{date}**. The recommended amount of water is **{water_amount} mm**."

            llm_prompt = f"The user asked: '{query}'.\nThe result from the irrigation tool is:\n{tool_output}\nPlease answer clearly and concisely, focusing on the irrigation recommendation."
        else:
            llm_prompt = f"The user asked: '{query}', but no valid field name was found. Please prompt the user to specify a field like 'chlewi', 'bir ali', 'taba', 'sidi salah', 'metwiya', or 'menzel'."
            tool_output = "No field detected for irrigation query."

    # Check for areas/fields query
    elif any(term in query_lower for term in ["areas", "fields", "what are my fields", "show my areas", "الحقول", "المناطق"]):
        with st.spinner("Retrieving your areas and fields..."):
            user_areas = get_all_user_areas_with_children(seabex_user_id) # Pass seabex_user_id
        
        if user_areas:
            area_names = []
            for area in user_areas:
                # Access 'area_name' and 'child_name' as per the API response structure
                area_name = area.get("area_name", "Unknown Area")
                # Filter out children that don't have a 'child_name' or are otherwise malformed
                fields = sorted([field.get("child_name") for field in area.get("children", []) if isinstance(field, dict) and field.get("child_name")])
                if fields:
                    area_names.append(f"- **{area_name}**: includes fields such as {', '.join(fields)}.")
                else:
                    area_names.append(f"- **{area_name}**: no specific fields listed.")
            
            if area_names:
                tool_output = "Here are your registered areas and their associated fields:\n" + "\n".join(area_names)
            else:
                tool_output = "No areas or fields found for your account."
            
            llm_prompt = f"The user asked: '{query}'.\nThe result from the user areas tool is:\n{tool_output}\nPlease summarize the available areas and fields for the user clearly. List fields alphabetically within each area."
        else:
            tool_output = "Could not retrieve user areas. There might be an issue with the API or no areas are registered."
            llm_prompt = f"The user asked: '{query}'.\nInformation from the user areas tool could not be retrieved. Please inform the user that their areas could not be fetched and suggest they check their account or try again."

    else:
        llm_prompt = f"The user asked: '{query}'. Provide a helpful and concise response regarding smart farming or general queries."

    messages = [
        {"role": "system", "content": "You are Magonia, a helpful smart farming assistant. Keep your responses concise and to the point. When listing fields, list them clearly."},
        {"role": "user", "content": llm_prompt}
    ]

    st.markdown("---")
    st.info("🧠 Sending request to Ollama...")
    return ollama_client.generate_response(messages)


# ---------- Streamlit Application ----------
def main():
    st.set_page_config(page_title="Magonia Smart Farming Assistant 🌿", page_icon="💧")

    st.title("Magonia Smart Farming Assistant 🌿")
    st.markdown("""
        Welcome to your smart farming assistant! I can help you determine irrigation needs for your fields
        or list your registered areas and fields.
        ---
    """)

    # Initialize Ollama client
    if 'ollama_client' not in st.session_state:
        st.session_state.ollama_client = OllamaClient(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL)

    # Initialize LiveKit client if credentials are available
    if 'livekit_client' not in st.session_state and LIVEKIT_API_KEY and LIVEKIT_API_SECRET:
        st.session_state.livekit_client = LiveKitClient(
            api_key=LIVEKIT_API_KEY,
            api_secret=LIVEKIT_API_SECRET,
            url=LIVEKIT_URL
        )
        
    # Add LiveKit UI elements in sidebar
    st.sidebar.header("LiveKit Video Chat")
    if LIVEKIT_API_KEY and LIVEKIT_API_SECRET:
        user_name = st.sidebar.text_input("Your Name", value="Farmer", key="livekit_user_name")
        room_name = st.sidebar.text_input("Room Name", value="farming-room", key="livekit_room_name")
        
        if st.sidebar.button("Join Video Chat"):
            if 'livekit_client' in st.session_state:
                # Create room and token
                st.session_state.livekit_client.create_room(room_name)
                token = st.session_state.livekit_client.create_token(user_name, room_name)
                
                if token:
                    # Display join link or embed iframe
                    join_url = f"{LIVEKIT_URL.replace('wss://', 'https://')}/join/{room_name}?token={token}"
                    st.sidebar.success("Room created! Click the link below to join:")
                    st.sidebar.markdown(f"[Join Video Chat]({join_url})")
    else:
        st.sidebar.warning("LiveKit credentials not configured. Add LIVEKIT_API_KEY and LIVEKIT_API_SECRET to your .env file.")
    
    ollama = st.session_state.ollama_client

    # Check Ollama model availability
    available_models = ollama.get_available_models()
    if not available_models:
        st.error(f"❌ Could not connect to Ollama at {OLLAMA_BASE_URL}. Please ensure Ollama is running and accessible.")
        st.stop()
    
    # Ollama Model selection in sidebar
    current_ollama_model_index = available_models.index(ollama.model) if ollama.model in available_models else 0
    selected_model = st.sidebar.selectbox(
        "Select an Ollama Model:", 
        available_models,
        index=current_ollama_model_index,
        key="ollama_model_selector"
    )
    if selected_model != ollama.model:
        st.session_state.ollama_client.model = selected_model
        st.rerun()
    st.sidebar.info(f"Using: **{st.session_state.ollama_client.model}**")

    st.sidebar.header("Settings")
    # Ollama Base URL input
    new_ollama_base_url = st.sidebar.text_input("Ollama Base URL", value=OLLAMA_BASE_URL, key="ollama_base_url_input")
    if new_ollama_base_url != ollama.base_url:
        st.session_state.ollama_client.base_url = new_ollama_base_url
        st.rerun()

    # Seabex User ID input
    current_seabex_user_id = st.sidebar.text_input("Seabex User ID", value=DEFAULT_SEABEX_USER_ID, key="seabex_user_id_input")


    st.markdown("---")
    user_question = st.text_input(
        "Ask me a question (e.g., 'Do I need to irrigate chlewi on 2024-07-20?' or 'What are my fields?')",
        key="user_query"
    )

    if st.button("Get Recommendation"):
        if not user_question:
            st.warning("Please enter a question.")
        else:
            with st.status("Processing your request...", expanded=True) as status:
                st.write("Analyzing your query...")
                
                final_response = process_user_query_with_tools(user_question, st.session_state.ollama_client, current_seabex_user_id)
                
                status.update(label="Request processed!", state="complete", expanded=False)
                st.success("✅ Final Answer:")
                st.write(final_response)

    st.markdown("---")
    st.write("Examples of questions you can ask:")
    st.markdown("- `Do I need to irrigate chlewi on 2024-07-20?`")
    st.markdown("- `What are my areas?`")
    st.markdown("- `Show me my fields.`")
    st.markdown("- `What is the weather like?` (for general LLM response)")
    st.caption("Developed with Streamlit and powered by Ollama and Seabex API.")

if __name__ == "__main__":
    main()
