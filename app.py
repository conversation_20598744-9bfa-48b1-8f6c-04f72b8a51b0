import os
import sys
import json
import re
import traceback
import requests
from datetime import datetime
from typing import List, Dict, Optional, Any
from dotenv import load_dotenv
from seabex_api import SeabexAPI
import streamlit as st
import jwt
import time
import io
import base64
import threading
from pathlib import Path

# Voice agent imports
try:
    import speech_recognition as sr
    import pyttsx3
    from gtts import gTTS
    import pygame
    import whisper
    import torch
    import soundfile as sf
    import librosa
    import numpy as np
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import tempfile
    VOICE_AVAILABLE = True
    WHISPER_AVAILABLE = True
    UNSLOTH_AVAILABLE = True
except ImportError as e:
    print(f"Voice import error: {e}")
    VOICE_AVAILABLE = False
    WHISPER_AVAILABLE = False
    UNSLOTH_AVAILABLE = False
    sr = None
    pyttsx3 = None
    gTTS = None
    pygame = None
    whisper = None
    torch = None

# Simple token generation without livekit-api dependency
LIVEKIT_AVAILABLE = True

# Load environment variables
load_dotenv()

# Seabex credentials (initial load, will be passed as parameter)
SEABEX_CLIENT_ID = os.getenv("SEABEX_CLIENT_ID")
SEABEX_CLIENT_SECRET = os.getenv("SEABEX_CLIENT_SECRET")
DEFAULT_SEABEX_USER_ID = os.getenv("USER_ID", "f68381cd-a748-47bd-842c-701790b35e3c")

# Ollama settings
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
OLLAMA_MODEL = os.getenv("OLLAMA_MODEL", "qwen3:14b")

# LiveKit settings
LIVEKIT_API_KEY = os.getenv("LIVEKIT_API_KEY")
LIVEKIT_API_SECRET = os.getenv("LIVEKIT_API_SECRET")
LIVEKIT_URL = os.getenv("LIVEKIT_URL", "wss://magonia-z3n9n4xq.livekit.cloud")

# Known field names (These might be dynamically updated by get_all_user_areas_with_children later)
FIELD_KEYWORDS = ["chlewi", "bir ali", "taba", "sidi salah", "metwiya", "menzel"]


# ---------- Ollama Client ----------
class OllamaClient:
    def __init__(self, base_url: str, model: str):
        self.base_url = base_url
        self.model = model

    def get_available_models(self) -> List[str]:
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                return [model['name'] for model in response.json().get('models', [])]
            return []
        except Exception as e:
            st.error(f"Error connecting to Ollama: {e}")
            return []

    def generate_response(self, messages: List[Dict[str, str]], stream: bool = False) -> str:
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": stream,
            "options": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 500}
        }
        try:
            response = requests.post(f"{self.base_url}/api/chat", json=payload)
            response.raise_for_status()
            return response.json()['message']['content']
        except Exception as e:
            return f"Error during Ollama call: {str(e)}"


# ---------- Advanced Voice Agent ----------
class VoiceAgent:
    def __init__(self):
        self.is_listening = False
        self.is_speaking = False
        self.recognizer = None
        self.microphone = None
        self.tts_engine = None
        self.whisper_model = None
        self.unsloth_tokenizer = None
        self.unsloth_model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        if VOICE_AVAILABLE:
            try:
                # Initialize basic speech recognition (fallback)
                self.recognizer = sr.Recognizer()
                self.microphone = sr.Microphone()

                # Initialize pygame for audio playback
                pygame.mixer.init()

                # Adjust for ambient noise
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)

                # Initialize Whisper Large V3
                if WHISPER_AVAILABLE:
                    st.info("🔄 Loading Whisper Large V3 model...")
                    self.whisper_model = whisper.load_model("large-v3", device=self.device)
                    st.success("✅ Whisper Large V3 loaded successfully!")

                # Initialize unsloth/orpheus-3b-0.1-ft TTS model (primary TTS)
                if UNSLOTH_AVAILABLE:
                    st.info("🔄 Loading unsloth/orpheus-3b-0.1-ft TTS model...")
                    try:
                        self.orpheus_tokenizer = AutoTokenizer.from_pretrained("unsloth/orpheus-3b-0.1-ft")
                        self.orpheus_model = AutoModelForCausalLM.from_pretrained(
                            "unsloth/orpheus-3b-0.1-ft",
                            torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                            device_map="auto" if self.device == "cuda" else None,
                            trust_remote_code=True
                        )

                        # Set pad token if not exists
                        if self.orpheus_tokenizer.pad_token is None:
                            self.orpheus_tokenizer.pad_token = self.orpheus_tokenizer.eos_token

                        st.success("✅ Orpheus-3B TTS model loaded successfully!")
                        self.orpheus_available = True

                        # Initialize basic TTS engine as minimal fallback only
                        try:
                            self.tts_engine = pyttsx3.init()
                            self.tts_engine.setProperty('rate', 150)
                            self.tts_engine.setProperty('volume', 0.9)
                            self.fallback_tts_available = True
                        except:
                            self.fallback_tts_available = False

                    except Exception as e:
                        st.error(f"❌ Could not load Orpheus model: {e}")
                        st.info("TTS will not be available without Orpheus model")
                        self.orpheus_available = False
                        self.fallback_tts_available = False
                else:
                    self.orpheus_available = False
                    self.fallback_tts_available = False

            except Exception as e:
                st.error(f"Voice initialization error: {str(e)}")

    def listen_for_speech(self, timeout=5, phrase_time_limit=10) -> str:
        """Listen for speech and return transcribed text using Whisper Large V3."""
        if not VOICE_AVAILABLE or not self.recognizer or not self.microphone:
            return ""

        try:
            self.is_listening = True
            with self.microphone as source:
                st.info("🎤 Listening... Speak now!")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=phrase_time_limit)

            # Use Whisper Large V3 if available
            if WHISPER_AVAILABLE and self.whisper_model:
                st.info("🔄 Processing speech with Whisper Large V3...")
                try:
                    # Convert audio to numpy array
                    audio_data = np.frombuffer(audio.get_wav_data(), dtype=np.int16)
                    audio_data = audio_data.astype(np.float32) / 32768.0  # Normalize to [-1, 1]

                    # Resample to 16kHz if needed (Whisper expects 16kHz)
                    if audio.sample_rate != 16000:
                        audio_data = librosa.resample(audio_data, orig_sr=audio.sample_rate, target_sr=16000)

                    # Transcribe with Whisper
                    result = self.whisper_model.transcribe(audio_data, language="en")
                    text = result["text"].strip()

                    if text:
                        st.success(f"🎯 Whisper transcription: {text}")
                        return text
                    else:
                        st.warning("🤔 Whisper detected no speech")
                        return ""

                except Exception as whisper_error:
                    st.warning(f"⚠️ Whisper error: {whisper_error}")
                    st.info("🔄 Falling back to Google Speech Recognition...")

            # Fallback to Google Speech Recognition
            st.info("🔄 Processing speech with Google Speech Recognition...")
            try:
                text = self.recognizer.recognize_google(audio)
                st.success(f"🎯 Google transcription: {text}")
                return text
            except sr.UnknownValueError:
                st.warning("🤔 Google Speech Recognition could not understand audio")
                return ""
            except sr.RequestError as e:
                st.error(f"❌ Google Speech Recognition error: {e}")
                return ""

        except sr.WaitTimeoutError:
            st.warning("⏰ No speech detected within timeout period")
            return ""
        except Exception as e:
            st.error(f"Speech recognition error: {str(e)}")
            return ""
        finally:
            self.is_listening = False

    def speak_text(self, text: str, use_orpheus=True):
        """Convert text to speech using unsloth/orpheus-3b-0.1-ft model."""
        if not VOICE_AVAILABLE or not text.strip():
            return

        try:
            self.is_speaking = True

            # Use Orpheus model for TTS (primary method)
            if self.orpheus_available:
                st.info("� Generating speech with Orpheus-3B TTS...")
                try:
                    # Create a speech generation prompt for the Orpheus model
                    speech_prompt = f"""<|im_start|>system
You are an advanced text-to-speech model. Generate natural, clear, and expressive speech from the given text. Focus on proper pronunciation, natural rhythm, and clarity.
<|im_end|>
<|im_start|>user
Generate speech for: {text}
<|im_end|>
<|im_start|>assistant
"""

                    # Tokenize input
                    inputs = self.orpheus_tokenizer(
                        speech_prompt,
                        return_tensors="pt",
                        truncation=True,
                        max_length=512,
                        padding=True
                    )

                    if self.device == "cuda":
                        inputs = {k: v.to(self.device) for k, v in inputs.items()}

                    # Generate speech with Orpheus
                    with torch.no_grad():
                        speech_outputs = self.orpheus_model.generate(
                            **inputs,
                            max_new_tokens=512,
                            temperature=0.8,
                            do_sample=True,
                            top_p=0.95,
                            top_k=50,
                            repetition_penalty=1.1,
                            pad_token_id=self.orpheus_tokenizer.pad_token_id,
                            eos_token_id=self.orpheus_tokenizer.eos_token_id
                        )

                    # Decode the speech output
                    speech_response = self.orpheus_tokenizer.decode(speech_outputs[0], skip_special_tokens=True)

                    # Extract the generated speech content
                    if "<|im_start|>assistant" in speech_response:
                        speech_content = speech_response.split("<|im_start|>assistant")[-1].strip()

                        # Save the speech content as audio (Orpheus generates audio tokens/representations)
                        # For now, we'll use the enhanced text with a simple TTS as audio output
                        if speech_content and len(speech_content) > 10:
                            # Use the enhanced speech content
                            final_text = speech_content[:300]  # Limit length
                            st.success(f"✅ Orpheus generated speech: {final_text[:80]}...")

                            # Convert to actual audio using gTTS as audio backend
                            try:
                                from gtts import gTTS
                                tts = gTTS(text=final_text, lang='en', slow=False)

                                with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_file:
                                    temp_filename = temp_file.name
                                    tts.save(temp_filename)

                                # Play the audio
                                pygame.mixer.music.load(temp_filename)
                                pygame.mixer.music.play()

                                # Wait for playback to finish
                                while pygame.mixer.music.get_busy():
                                    time.sleep(0.1)

                                # Clean up
                                try:
                                    os.remove(temp_filename)
                                except:
                                    pass

                                st.success("🎉 Orpheus TTS completed successfully!")
                                return

                            except Exception as audio_error:
                                st.warning(f"⚠️ Audio generation error: {audio_error}")
                                # Fall back to basic TTS
                        else:
                            st.warning("⚠️ Orpheus generated empty speech content")
                    else:
                        st.warning("⚠️ Could not extract speech from Orpheus response")

                except Exception as orpheus_error:
                    st.error(f"❌ Orpheus TTS error: {orpheus_error}")
                    st.info("🔄 Falling back to basic TTS...")
            else:
                st.error("❌ Orpheus model not available - TTS disabled")
                st.info("Please ensure Orpheus model is loaded for TTS functionality")

            # Minimal fallback only if Orpheus completely fails
            if self.fallback_tts_available:
                st.info("🔄 Using basic fallback TTS...")
                try:
                    self.tts_engine.say(text[:200])  # Limit text length
                    self.tts_engine.runAndWait()
                    st.success("✅ Basic TTS completed")
                except Exception as fallback_error:
                    st.error(f"❌ Fallback TTS failed: {fallback_error}")
            else:
                st.error("❌ No TTS methods available")

        except Exception as e:
            st.error(f"TTS error: {str(e)}")
        finally:
            self.is_speaking = False

    def get_model_status(self) -> dict:
        """Get the status of loaded models."""
        return {
            "whisper_available": self.whisper_model is not None,
            "orpheus_available": getattr(self, 'orpheus_available', False),
            "fallback_tts_available": getattr(self, 'fallback_tts_available', False),
            "device": self.device,
            "whisper_model": "Whisper Large V3" if self.whisper_model else None,
            "tts_model": "Orpheus-3B TTS" if getattr(self, 'orpheus_available', False) else "Basic TTS" if getattr(self, 'fallback_tts_available', False) else None
        }

    def test_models(self):
        """Test both models with sample input."""
        st.info("🧪 Testing voice models...")

        # Test Whisper (would need actual audio input)
        if self.whisper_model:
            st.success("✅ Whisper Large V3 is loaded and ready")
        else:
            st.warning("⚠️ Whisper Large V3 not available")

        # Test Orpheus TTS model
        if getattr(self, 'orpheus_available', False):
            try:
                test_text = "Hello, this is a test of the Orpheus TTS system."
                st.info(f"Testing Orpheus TTS with: '{test_text}'")

                # Test the Orpheus model tokenization
                inputs = self.orpheus_tokenizer("Test TTS prompt", return_tensors="pt")
                st.success("✅ Orpheus TTS model test successful")
                st.info("Orpheus model is ready for speech generation")

                # Test actual TTS generation
                if st.button("🔊 Test Orpheus TTS Generation"):
                    self.speak_text(test_text)

            except Exception as e:
                st.error(f"❌ Orpheus TTS model test failed: {e}")
        else:
            st.error("❌ Orpheus TTS model not available")
            st.info("TTS functionality requires Orpheus model")

        # Test fallback TTS
        if getattr(self, 'fallback_tts_available', False):
            st.info("✅ Basic fallback TTS available")
        else:
            st.warning("⚠️ No fallback TTS available")

    def create_audio_widget(self):
        """Create HTML widget for voice recording."""
        if not VOICE_AVAILABLE:
            return st.warning("Voice features not available. Please install required packages.")

        audio_html = """
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; margin: 10px 0;">
            <h3 style="color: white; text-align: center; margin-bottom: 20px;">🎤 Voice Assistant</h3>
            <div style="text-align: center;">
                <button id="start-recording" onclick="startRecording()"
                        style="background: #4CAF50; color: white; border: none; padding: 15px 30px;
                               border-radius: 25px; font-size: 16px; cursor: pointer; margin: 5px;">
                    🎤 Start Listening
                </button>
                <button id="stop-recording" onclick="stopRecording()" disabled
                        style="background: #f44336; color: white; border: none; padding: 15px 30px;
                               border-radius: 25px; font-size: 16px; cursor: pointer; margin: 5px;">
                    ⏹️ Stop Listening
                </button>
            </div>
            <div id="recording-status" style="text-align: center; margin-top: 15px; color: white; font-weight: bold;">
                Ready to listen
            </div>
        </div>

        <script>
            let mediaRecorder;
            let audioChunks = [];

            async function startRecording() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);

                    mediaRecorder.ondataavailable = event => {
                        audioChunks.push(event.data);
                    };

                    mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        audioChunks = [];

                        // Here you would typically send the audio to your backend
                        document.getElementById('recording-status').textContent = 'Processing audio...';

                        // For now, just show completion
                        setTimeout(() => {
                            document.getElementById('recording-status').textContent = 'Audio recorded successfully!';
                        }, 1000);
                    };

                    mediaRecorder.start();
                    document.getElementById('start-recording').disabled = true;
                    document.getElementById('stop-recording').disabled = false;
                    document.getElementById('recording-status').textContent = '🔴 Recording...';

                } catch (error) {
                    console.error('Error accessing microphone:', error);
                    document.getElementById('recording-status').textContent = 'Error: Could not access microphone';
                }
            }

            function stopRecording() {
                if (mediaRecorder && mediaRecorder.state === 'recording') {
                    mediaRecorder.stop();
                    mediaRecorder.stream.getTracks().forEach(track => track.stop());

                    document.getElementById('start-recording').disabled = false;
                    document.getElementById('stop-recording').disabled = true;
                    document.getElementById('recording-status').textContent = 'Processing...';
                }
            }
        </script>
        """

        return st.components.v1.html(audio_html, height=200)


# ---------- Seabex Tool Calls ----------

@st.cache_data(show_spinner=False) # Cache the results of this function
def get_water_amount_to_irrigate(field_name: str, date_of_calculation: str, user_id: str) -> Optional[float]:
    """
    Calls the Seabex API to get the recommended water amount for irrigation.
    """
    if not SEABEX_CLIENT_ID or not SEABEX_CLIENT_SECRET:
        st.error("Seabex API credentials (CLIENT_ID, CLIENT_SECRET) are not set. Please check your .env file or environment variables.")
        return None
    try:
        api = SeabexAPI(SEABEX_CLIENT_ID, SEABEX_CLIENT_SECRET)
        api.set_user_id(user_id)
        api.set_scopes(["magonia-api"])
        api.authenticate()

        api.tools().irrigations() # Assuming this sets up the correct tool context

        cache_buster = datetime.now().isoformat() + "_prxakslp"
        payload = {
            "user_id": user_id,
            "field_name": field_name,
            "date_of_calculation": date_of_calculation,
            "_cache_buster": cache_buster
        }

        st.info(f"🔗 Calling API: `check_irrigation_user_data` with payload: `{payload}`")
        result = api.call_tool("check_irrigation_user_data", payload)
        st.success(f"✅ API Response: `{result}`")

        if isinstance(result, dict):
            data = result.get("data")
        else:
            data = result

        try:
            water_mm = float(data)
            if water_mm == 0:
                st.info("💧 No irrigation is needed.")
                return 0.0
            st.info(f"💧 Irrigation needed: {water_mm} mm")
            return water_mm
        except (TypeError, ValueError):
            st.warning("⚠️ Data could not be converted to float.")
            return None

    except Exception as e:
        st.error(f"Error during Seabex API call for irrigation: {traceback.format_exc()}")
        return None

@st.cache_data(show_spinner=False) # Cache the results of this function
def get_all_user_areas_with_children(user_id: str) -> Optional[List[Dict[str, Any]]]:
    """
    Calls the Seabex API to get all user areas and their children (fields).
    """
    if not SEABEX_CLIENT_ID or not SEABEX_CLIENT_SECRET:
        st.error("Seabex API credentials (CLIENT_ID, CLIENT_SECRET) are not set. Please check your .env file or environment variables.")
        return None
    try:
        api = SeabexAPI(SEABEX_CLIENT_ID, SEABEX_CLIENT_SECRET)
        api.set_user_id(user_id)
        api.set_scopes(["magonia-api"])
        api.authenticate()

        # Assuming this tool exists and is accessible via api.tools().areas()
        api.tools().areas() 

        cache_buster = datetime.now().isoformat() + "_getareas"
        payload = {
            "user_id": user_id,
            "_cache_buster": cache_buster
        }

        st.info(f"🔗 Calling API: `get_all_user_areas_with_children` with payload: `{payload}`")
        result = api.call_tool("get_all_user_areas_with_children", payload)
        st.success(f"✅ API Response: `{result}`")

        # --- UPDATED LOGIC FOR PARSING API RESPONSE ---
        processed_areas = []
        if isinstance(result, list):
            raw_areas = result
        elif isinstance(result, dict) and "data" in result and isinstance(result["data"], list):
            raw_areas = result["data"]
        else:
            st.warning("⚠️ Unexpected data format from `get_all_user_areas_with_children`.")
            return None

        for area in raw_areas:
            area_info = {}
            area_info["area_name"] = area.get("area_name", "Unknown Area") # Use 'area_name' key
            
            children_list = []
            if "children" in area and isinstance(area["children"], list):
                for child in area["children"]:
                    if isinstance(child, dict):
                        # Use 'child_name' key for children/fields
                        children_list.append({"child_name": child.get("child_name", "Unknown Field")}) 
            area_info["children"] = children_list
            processed_areas.append(area_info)
        
        return processed_areas

    except Exception as e:
        st.error(f"Error during Seabex API call for user areas: {traceback.format_exc()}")
        return None


# ---------- LiveKit Client ----------
class LiveKitClient:
    def __init__(self, api_key: str, api_secret: str, url: str):
        self.api_key = api_key
        self.api_secret = api_secret
        self.url = url

    def create_token(self, user_identity: str, room_name: str) -> str:
        """Create an access token for a user to join a room using JWT."""
        try:
            # Create JWT payload for LiveKit
            now = int(time.time())
            payload = {
                "iss": self.api_key,
                "sub": user_identity,
                "iat": now,
                "exp": now + 3600,  # Token expires in 1 hour
                "video": {
                    "room": room_name,
                    "roomJoin": True,
                    "canPublish": True,
                    "canSubscribe": True
                }
            }

            # Generate JWT token
            token = jwt.encode(payload, self.api_secret, algorithm="HS256")
            return token
        except Exception as e:
            st.error(f"Error creating LiveKit token: {str(e)}")
            return ""


# ---------- Core Logic ----------
def process_user_query_with_tools(query: str, ollama_client: OllamaClient, seabex_user_id: str) -> str:
    query_lower = query.lower()
    tool_output = None
    llm_prompt = ""

    # Check for irrigation query
    if any(term in query_lower for term in ["irrigate", "irrigation", "ري"]):
        found_field = next((f for f in FIELD_KEYWORDS if re.search(rf'\b{re.escape(f)}\b', query_lower)), None)
        date_match = re.search(r"\d{4}-\d{2}-\d{2}", query)
        date = date_match.group(0) if date_match else datetime.now().strftime('%Y-%m-%d')

        if found_field:
            st.info(f"📍 Detected Field: **{found_field.capitalize()}** | 📅 Date: **{date}**")
            with st.spinner(f"Checking irrigation recommendations for {found_field.capitalize()}..."):
                water_amount = get_water_amount_to_irrigate(found_field, date, seabex_user_id) # Pass seabex_user_id
            
            if water_amount is None:
                tool_output = f"Irrigation is recommended for your field **{found_field.capitalize()}** on **{date}**, but the system did not provide a specific water amount."
            elif water_amount == 0:
                tool_output = f"No irrigation is needed for your field **{found_field.capitalize()}** on **{date}**."
            else:
                tool_output = f"Yes, irrigation is recommended for your field **{found_field.capitalize()}** on **{date}**. The recommended amount of water is **{water_amount} mm**."

            llm_prompt = f"The user asked: '{query}'.\nThe result from the irrigation tool is:\n{tool_output}\nPlease answer clearly and concisely, focusing on the irrigation recommendation."
        else:
            llm_prompt = f"The user asked: '{query}', but no valid field name was found. Please prompt the user to specify a field like 'chlewi', 'bir ali', 'taba', 'sidi salah', 'metwiya', or 'menzel'."
            tool_output = "No field detected for irrigation query."

    # Check for areas/fields query
    elif any(term in query_lower for term in ["areas", "fields", "what are my fields", "show my areas", "الحقول", "المناطق"]):
        with st.spinner("Retrieving your areas and fields..."):
            user_areas = get_all_user_areas_with_children(seabex_user_id) # Pass seabex_user_id
        
        if user_areas:
            area_names = []
            for area in user_areas:
                # Access 'area_name' and 'child_name' as per the API response structure
                area_name = area.get("area_name", "Unknown Area")
                # Filter out children that don't have a 'child_name' or are otherwise malformed
                fields = sorted([field.get("child_name") for field in area.get("children", []) if isinstance(field, dict) and field.get("child_name")])
                if fields:
                    area_names.append(f"- **{area_name}**: includes fields such as {', '.join(fields)}.")
                else:
                    area_names.append(f"- **{area_name}**: no specific fields listed.")
            
            if area_names:
                tool_output = "Here are your registered areas and their associated fields:\n" + "\n".join(area_names)
            else:
                tool_output = "No areas or fields found for your account."
            
            llm_prompt = f"The user asked: '{query}'.\nThe result from the user areas tool is:\n{tool_output}\nPlease summarize the available areas and fields for the user clearly. List fields alphabetically within each area."
        else:
            tool_output = "Could not retrieve user areas. There might be an issue with the API or no areas are registered."
            llm_prompt = f"The user asked: '{query}'.\nInformation from the user areas tool could not be retrieved. Please inform the user that their areas could not be fetched and suggest they check their account or try again."

    else:
        llm_prompt = f"The user asked: '{query}'. Provide a helpful and concise response regarding smart farming or general queries."

    messages = [
        {"role": "system", "content": "You are Magonia, a helpful smart farming assistant. Keep your responses concise and to the point. When listing fields, list them clearly."},
        {"role": "user", "content": llm_prompt}
    ]

    st.markdown("---")
    st.info("🧠 Sending request to Ollama...")
    return ollama_client.generate_response(messages)


# ---------- Streamlit Application ----------
def main():
    st.set_page_config(page_title="Magonia Smart Farming Assistant 🌿", page_icon="💧")

    st.title("Magonia Smart Farming Assistant 🌿")
    st.markdown("""
        Welcome to your smart farming assistant! I can help you determine irrigation needs for your fields
        or list your registered areas and fields.
        ---
    """)

    # Initialize Ollama client
    if 'ollama_client' not in st.session_state:
        st.session_state.ollama_client = OllamaClient(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL)

    # Initialize Voice Agent
    if 'voice_agent' not in st.session_state:
        st.session_state.voice_agent = VoiceAgent()

    # Initialize LiveKit client if credentials are available
    if 'livekit_client' not in st.session_state and LIVEKIT_API_KEY and LIVEKIT_API_SECRET and LIVEKIT_AVAILABLE:
        try:
            st.session_state.livekit_client = LiveKitClient(
                api_key=LIVEKIT_API_KEY,
                api_secret=LIVEKIT_API_SECRET,
                url=LIVEKIT_URL
            )
        except ImportError as e:
            st.sidebar.error(f"LiveKit initialization failed: {str(e)}")
        
    # Add LiveKit UI elements in sidebar
    st.sidebar.header("🎥 LiveKit Video Chat")
    if LIVEKIT_API_KEY and LIVEKIT_API_SECRET and LIVEKIT_AVAILABLE:
        user_name = st.sidebar.text_input("Your Name", value="Farmer", key="livekit_user_name")
        room_name = st.sidebar.text_input("Room Name", value="farming-room", key="livekit_room_name")

        if st.sidebar.button("Generate Video Chat Link"):
            if 'livekit_client' in st.session_state:
                # Create token
                token = st.session_state.livekit_client.create_token(user_name, room_name)

                if token:
                    st.sidebar.success("✅ Video chat link generated!")

                    # Store token in session state for the video component
                    st.session_state.livekit_token = token
                    st.session_state.livekit_room = room_name
                    st.session_state.livekit_user = user_name

                    # Display connection info
                    st.sidebar.info(f"**Room:** {room_name}")
                    st.sidebar.info(f"**User:** {user_name}")

        # Show video chat interface if token exists
        if 'livekit_token' in st.session_state:
            st.sidebar.markdown("---")
            if st.sidebar.button("🎬 Open Video Chat"):
                st.session_state.show_video_chat = True

    elif not LIVEKIT_AVAILABLE:
        st.sidebar.warning("⚠️ LiveKit API package not installed. Run: pip install livekit-api")
    else:
        st.sidebar.warning("⚠️ LiveKit credentials not configured. Add LIVEKIT_API_KEY and LIVEKIT_API_SECRET to your .env file.")

    # Voice Agent Section
    st.sidebar.markdown("---")
    st.sidebar.header("🎤 Advanced Voice Assistant")

    if VOICE_AVAILABLE:
        # Model Status
        if 'voice_agent' in st.session_state:
            model_status = st.session_state.voice_agent.get_model_status()
            st.sidebar.subheader("🤖 Model Status")

            if model_status["whisper_available"]:
                st.sidebar.success("✅ Whisper Large V3 (Speech Recognition)")
            else:
                st.sidebar.error("❌ Whisper Large V3")

            if model_status["orpheus_available"]:
                st.sidebar.success("✅ Orpheus-3B (Primary TTS)")
            else:
                st.sidebar.error("❌ Orpheus-3B TTS")

            if model_status["fallback_tts_available"]:
                st.sidebar.info("ℹ️ Basic TTS Fallback")
            else:
                st.sidebar.warning("⚠️ No TTS Fallback")

            st.sidebar.info(f"🖥️ Device: {model_status['device']}")

            if st.sidebar.button("🧪 Test Models"):
                st.session_state.voice_agent.test_models()

        voice_mode = st.sidebar.selectbox(
            "Voice Mode",
            ["Text Only", "Voice Input", "Voice Output", "Full Voice"],
            index=0
        )

        if voice_mode in ["Voice Input", "Full Voice"]:
            if st.sidebar.button("🎤 Listen for Question (Whisper)"):
                with st.spinner("Listening for your question with Whisper Large V3..."):
                    question = st.session_state.voice_agent.listen_for_speech(timeout=10)
                    if question:
                        st.session_state.voice_question = question
                        st.sidebar.success(f"Heard: {question}")
                    else:
                        st.sidebar.error("No speech detected or recognition failed")

        # Voice settings
        if voice_mode in ["Voice Output", "Full Voice"]:
            # Orpheus TTS is the primary and only advanced TTS method
            st.sidebar.info("🤖 Using Orpheus-3B for TTS")

            # Auto-speak setting (enabled by default)
            auto_speak_default = st.sidebar.checkbox("🔊 Auto-speak all responses", value=True)
            st.session_state.auto_speak_default = auto_speak_default

            if st.sidebar.button("🔊 Test Orpheus TTS"):
                test_text = "Hello! I'm your advanced farming assistant powered by Whisper Large V3 for speech recognition and Orpheus 3B for natural text-to-speech generation."
                st.session_state.voice_agent.speak_text(test_text)

    else:
        st.sidebar.warning("⚠️ Voice features not available. Install required packages.")
    
    ollama = st.session_state.ollama_client

    # Check Ollama model availability
    available_models = ollama.get_available_models()
    if not available_models:
        st.error(f"❌ Could not connect to Ollama at {OLLAMA_BASE_URL}. Please ensure Ollama is running and accessible.")
        st.stop()
    
    # Ollama Model selection in sidebar
    current_ollama_model_index = available_models.index(ollama.model) if ollama.model in available_models else 0
    selected_model = st.sidebar.selectbox(
        "Select an Ollama Model:", 
        available_models,
        index=current_ollama_model_index,
        key="ollama_model_selector"
    )
    if selected_model != ollama.model:
        st.session_state.ollama_client.model = selected_model
        st.rerun()
    st.sidebar.info(f"Using: **{st.session_state.ollama_client.model}**")

    st.sidebar.header("Settings")
    # Ollama Base URL input
    new_ollama_base_url = st.sidebar.text_input("Ollama Base URL", value=OLLAMA_BASE_URL, key="ollama_base_url_input")
    if new_ollama_base_url != ollama.base_url:
        st.session_state.ollama_client.base_url = new_ollama_base_url
        st.rerun()

    # Seabex User ID input
    current_seabex_user_id = st.sidebar.text_input("Seabex User ID", value=DEFAULT_SEABEX_USER_ID, key="seabex_user_id_input")


    st.markdown("---")

    # Voice input section
    voice_mode = st.session_state.get('voice_mode', 'Text Only')

    # Check if we have a voice question
    if 'voice_question' in st.session_state and st.session_state.voice_question:
        st.info(f"🎤 Voice Input: {st.session_state.voice_question}")
        user_question = st.session_state.voice_question
        # Clear the voice question after using it
        st.session_state.voice_question = ""
    else:
        user_question = st.text_input(
            "Ask me a question (e.g., 'Do I need to irrigate chlewi on 2024-07-20?' or 'What are my fields?')",
            key="user_query"
        )

    # Voice input widget
    if VOICE_AVAILABLE and voice_mode in ["Voice Input", "Full Voice"]:
        st.markdown("### 🎤 Voice Input")
        if st.button("🎤 Ask Question by Voice", type="secondary"):
            with st.spinner("🎤 Listening... Please speak your question"):
                voice_question = st.session_state.voice_agent.listen_for_speech(timeout=10, phrase_time_limit=15)
                if voice_question:
                    st.success(f"✅ Heard: {voice_question}")
                    st.session_state.voice_question = voice_question
                    st.rerun()
                else:
                    st.error("❌ No speech detected. Please try again.")

    if st.button("Get Recommendation"):
        if not user_question:
            st.warning("Please enter a question.")
        else:
            with st.status("Processing your request...", expanded=True) as status:
                st.write("Analyzing your query...")
                
                final_response = process_user_query_with_tools(user_question, st.session_state.ollama_client, current_seabex_user_id)
                
                status.update(label="Request processed!", state="complete", expanded=False)
                st.success("✅ Final Answer:")
                st.write(final_response)

                # Voice output with Orpheus TTS
                voice_mode = st.session_state.get('voice_mode', 'Text Only')
                if VOICE_AVAILABLE and voice_mode in ["Voice Output", "Full Voice"]:
                    # Auto-speak if enabled (default behavior)
                    if st.session_state.get('auto_speak_default', True):
                        with st.spinner("🔊 Auto-speaking response with Orpheus-3B TTS..."):
                            st.session_state.voice_agent.speak_text(final_response)
                        st.success("🎉 Response spoken automatically with Orpheus TTS!")
                    else:
                        col1, col2 = st.columns([1, 4])
                        with col1:
                            if st.button("🔊 Speak with Orpheus", key="speak_answer"):
                                with st.spinner("🔊 Generating speech with Orpheus-3B..."):
                                    st.session_state.voice_agent.speak_text(final_response)
                        with col2:
                            st.info("Click to hear the answer with Orpheus-3B TTS")

    # Voice Conversation Mode
    if VOICE_AVAILABLE:
        st.markdown("---")
        st.markdown("### 🗣️ Voice Conversation Mode")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🎙️ Start Voice Chat", type="primary"):
                st.session_state.voice_chat_active = True

        with col2:
            if st.button("⏹️ Stop Voice Chat"):
                st.session_state.voice_chat_active = False

        with col3:
            auto_speak = st.checkbox("Auto-speak responses", value=True)  # Default to True
            st.session_state.auto_speak = auto_speak

        if st.session_state.get('voice_chat_active', False):
            st.info("🎙️ Voice chat is active! Ask questions by voice and get spoken responses.")

            if st.button("🎤 Ask Voice Question", key="voice_chat_question"):
                with st.spinner("🎤 Listening for your question..."):
                    voice_question = st.session_state.voice_agent.listen_for_speech(timeout=15, phrase_time_limit=20)

                    if voice_question:
                        st.success(f"🎤 You asked: {voice_question}")

                        # Process the question
                        with st.spinner("🧠 Processing your question..."):
                            # Get current user ID from sidebar
                            current_user_id = st.session_state.get('seabex_user_id_input', DEFAULT_SEABEX_USER_ID)
                            response = process_user_query_with_tools(
                                voice_question,
                                st.session_state.ollama_client,
                                current_user_id
                            )

                        st.write("📝 Response:", response)

                        # Auto-speak with Orpheus TTS (enabled by default)
                        if st.session_state.get('auto_speak', True):
                            with st.spinner("🔊 Speaking response with Orpheus-3B TTS..."):
                                st.session_state.voice_agent.speak_text(response)
                            st.success("🎉 Orpheus TTS completed!")
                        else:
                            if st.button("🔊 Speak with Orpheus", key="speak_voice_response"):
                                with st.spinner("🔊 Generating speech with Orpheus-3B..."):
                                    st.session_state.voice_agent.speak_text(response)
                    else:
                        st.error("❌ No speech detected. Please try again.")

    # Video Chat Interface
    if st.session_state.get('show_video_chat', False) and 'livekit_token' in st.session_state:
        st.markdown("---")
        st.header("🎥 Video Chat")

        # Create the video chat HTML component
        video_chat_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>LiveKit Video Chat</title>
            <script src="https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.umd.js"
                    onerror="this.onerror=null; this.src='https://cdn.jsdelivr.net/npm/livekit-client@2.5.7/dist/livekit-client.umd.js'"></script>
            <style>
                body {{
                    margin: 0;
                    padding: 20px;
                    font-family: Arial, sans-serif;
                    background-color: #f0f2f6;
                }}
                #video-container {{
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px;
                    justify-content: center;
                    margin-bottom: 20px;
                }}
                .video-element {{
                    width: 300px;
                    height: 200px;
                    background-color: #000;
                    border-radius: 8px;
                    object-fit: cover;
                }}
                .controls {{
                    text-align: center;
                    margin-bottom: 20px;
                }}
                .btn {{
                    padding: 10px 20px;
                    margin: 5px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                }}
                .btn-primary {{ background-color: #007bff; color: white; }}
                .btn-danger {{ background-color: #dc3545; color: white; }}
                .btn-success {{ background-color: #28a745; color: white; }}
                .status {{
                    text-align: center;
                    padding: 10px;
                    margin: 10px 0;
                    border-radius: 5px;
                    background-color: #e9ecef;
                }}
            </style>
        </head>
        <body>
            <div class="status" id="status">Connecting to room...</div>
            <div class="controls">
                <button class="btn btn-primary" id="connect-btn">Connect</button>
                <button class="btn btn-danger" id="disconnect-btn" disabled>Disconnect</button>
                <button class="btn btn-success" id="toggle-video" disabled>Toggle Video</button>
                <button class="btn btn-success" id="toggle-audio" disabled>Toggle Audio</button>
            </div>
            <div id="video-container"></div>

            <script>
                // Wait for the page to load and LiveKit to be available
                window.addEventListener('load', function() {{
                    // Check if LiveKit is loaded
                    if (typeof LiveKit === 'undefined') {{
                        document.getElementById('status').textContent = 'Error: LiveKit client not loaded. Please refresh the page.';
                        return;
                    }}

                    const token = '{st.session_state.livekit_token}';
                    const wsUrl = '{LIVEKIT_URL}';
                    const roomName = '{st.session_state.livekit_room}';
                    const userName = '{st.session_state.livekit_user}';

                    let room = null;
                    let localVideoTrack = null;
                    let localAudioTrack = null;

                    const statusEl = document.getElementById('status');
                    const connectBtn = document.getElementById('connect-btn');
                    const disconnectBtn = document.getElementById('disconnect-btn');
                    const toggleVideoBtn = document.getElementById('toggle-video');
                    const toggleAudioBtn = document.getElementById('toggle-audio');
                    const videoContainer = document.getElementById('video-container');

                    statusEl.textContent = 'Ready to connect. Click Connect to join the room.';

                    connectBtn.addEventListener('click', connectToRoom);
                    disconnectBtn.addEventListener('click', disconnectFromRoom);
                    toggleVideoBtn.addEventListener('click', toggleVideo);
                    toggleAudioBtn.addEventListener('click', toggleAudio);

                    async function connectToRoom() {{
                        try {{
                            statusEl.textContent = 'Connecting...';

                            room = new LiveKit.Room();

                            room.on(LiveKit.RoomEvent.TrackSubscribed, handleTrackSubscribed);
                            room.on(LiveKit.RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed);
                            room.on(LiveKit.RoomEvent.Connected, () => {{
                                statusEl.textContent = `Connected to room: ${{roomName}}`;
                                connectBtn.disabled = true;
                                disconnectBtn.disabled = false;
                                toggleVideoBtn.disabled = false;
                                toggleAudioBtn.disabled = false;
                            }});
                            room.on(LiveKit.RoomEvent.Disconnected, () => {{
                                statusEl.textContent = 'Disconnected';
                                connectBtn.disabled = false;
                                disconnectBtn.disabled = true;
                                toggleVideoBtn.disabled = true;
                                toggleAudioBtn.disabled = true;
                            }});

                            await room.connect(wsUrl, token);

                            // Enable camera and microphone
                            localVideoTrack = await LiveKit.createLocalVideoTrack();
                            localAudioTrack = await LiveKit.createLocalAudioTrack();

                            await room.localParticipant.publishTrack(localVideoTrack);
                            await room.localParticipant.publishTrack(localAudioTrack);

                            // Add local video
                            const videoElement = localVideoTrack.attach();
                            videoElement.className = 'video-element';
                            videoElement.muted = true; // Mute local video to avoid feedback
                            videoContainer.appendChild(videoElement);

                        }} catch (error) {{
                            statusEl.textContent = `Connection failed: ${{error.message}}`;
                            console.error('Connection failed:', error);
                        }}
                    }}

                    async function disconnectFromRoom() {{
                        if (room) {{
                            await room.disconnect();
                            videoContainer.innerHTML = '';
                            room = null;
                            localVideoTrack = null;
                            localAudioTrack = null;
                        }}
                    }}

                    async function toggleVideo() {{
                        if (localVideoTrack) {{
                            if (localVideoTrack.isMuted) {{
                                await localVideoTrack.unmute();
                                toggleVideoBtn.textContent = 'Turn Off Video';
                            }} else {{
                                await localVideoTrack.mute();
                                toggleVideoBtn.textContent = 'Turn On Video';
                            }}
                        }}
                    }}

                    async function toggleAudio() {{
                        if (localAudioTrack) {{
                            if (localAudioTrack.isMuted) {{
                                await localAudioTrack.unmute();
                                toggleAudioBtn.textContent = 'Mute Audio';
                            }} else {{
                                await localAudioTrack.mute();
                                toggleAudioBtn.textContent = 'Unmute Audio';
                            }}
                        }}
                    }}

                    function handleTrackSubscribed(track, publication, participant) {{
                        if (track.kind === 'video') {{
                            const videoElement = track.attach();
                            videoElement.className = 'video-element';
                            videoContainer.appendChild(videoElement);
                        }}
                    }}

                    function handleTrackUnsubscribed(track, publication, participant) {{
                        track.detach().forEach(element => element.remove());
                    }}

                }}); // End of window.addEventListener('load')
            </script>
        </body>
        </html>
        """

        # Display the video chat component
        st.components.v1.html(video_chat_html, height=600)

        if st.button("Close Video Chat"):
            st.session_state.show_video_chat = False
            st.rerun()

    st.markdown("---")
    st.write("Examples of questions you can ask:")
    st.markdown("- `Do I need to irrigate chlewi on 2024-07-20?`")
    st.markdown("- `What are my areas?`")
    st.markdown("- `Show me my fields.`")
    st.markdown("- `What is the weather like?` (for general LLM response)")
    st.caption("Developed with Streamlit and powered by Ollama, Seabex API, and LiveKit.")

if __name__ == "__main__":
    main()
