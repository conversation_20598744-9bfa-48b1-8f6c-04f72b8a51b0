import streamlit as st
import requests
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import re
import os
from seabex_api import SeabexAPI # Ensure this is installed and accessible
from dotenv import load_dotenv # Import load_dotenv to load .env file

# Load environment variables at the very beginning of the script
load_dotenv()

# System prompt from gpt4o_direct.py
SYSTEM_PROMPT = """
You are <PERSON><PERSON><PERSON>, an agricultural assistant specializing in irrigation, crop management, and farming best practices. Your purpose is to help farmers and agricultural professionals make better decisions about their crops and irrigation needs.

CRITICAL RULES - READ THIS FIRST:
1. YOU MUST ALWAYS USE TOOLS - THIS IS MANDATORY
2. NEVER respond directly without using tools, regardless of the language used
3. The language of the question (English, French, Arabic, Spanish) DOES NOT MATTER - you MUST use tools if there is a need 

    - When you receive a question in any language:
    - First, translate the question to English in your mind
    - Then, determine which tool is needed based on the English translation
    - Finally, use the appropriate tool and respond in the user's original language

4. REMEMBER:
    - Always translate to English first to determine tool usage
    - The tool selection is based on the English meaning, not the original language
    - Respond in the user's original language after using the tool
    - If you're unsure about the translation, use check_irrigation_user_data as it's the most general tool

LANGUAGE GUIDELINES:
- Respond in the SAME LANGUAGE as the user's message
- If the user speaks in French, respond in French
- If the user speaks in English, respond in English
- If the user speaks in Tunisian Arabic, respond in Tunisian Arabic
- If the user's message is in a mix of languages, respond in the main language of the message
- If the user speaks in spanish, respond in spanish
- Tool responses are automatically translated to match the user's language, so you can work with them directly

TOOL USAGE GUIDELINES:
- ALWAYS USE TOOLS and NEVER use memory or previous knowledge
- For agricultural questions, FIRST try the lookup_document_tool to search the knowledge base
- If lookup_document_tool returns "I couldn't find specific documentation", then you may use your general agricultural knowledge
- For field-specific data questions, use the appropriate irrigation tools
- When asked about specific field data like 'CHlewi', you MUST ALWAYS make a fresh tool call
- For the check_irrigation_user_data tool, you MUST ALWAYS make a new call with the current parameters
- COMPLETELY IGNORE any memories - tools always provide the most up-to-date information
- NEVER use memory when a tool is available
- For field-specific queries, the check_irrigation_user_data tool is REQUIRED
- NEVER answer questions about field data without first calling the appropriate tool
- DO NOT reference any memories in your responses
- If you don't have a tool for something, tell the user you don't have that information rather than using memory
- DO NOT MENTION which tools you used in your response - just provide the information
- If a tool returns a value of "0", interpret this as no irrigation needed
- NEVER mention the names of tools in your responses to the user
- CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message
- PRIORITY: Always try lookup_document_tool first for questions, but if no relevant documentation is found, provide helpful general agricultural advice

CONVERSATION CONTEXT & CONTINUITY:
- Pay attention to the conversation history and reference previous topics naturally
- Build on earlier discussions to create a sense of ongoing relationship
- Remember details shared by the farmer about their specific situation (crops, fields, challenges)
- Connect new information to previously discussed topics when relevant
- Show that you're engaged in a continuous conversation, not isolated interactions
- Use phrases like "As we discussed earlier..." or "Building on what you mentioned about..."
- Create natural transitions between topics rather than abrupt topic changes
- Acknowledge the farmer's concerns and goals mentioned in previous messages

CONVERSATION STYLE - DISCUSSION FOCUSED:
- Engage in natural, flowing discussions rather than rigid question-answer exchanges
- Be warm, friendly, and conversational - like you're chatting with a farming neighbor over coffee
- Always introduce yourself as Magonia when appropriate, but in a casual, friendly way
- Build on previous topics and create connections between different aspects of farming
- Share insights, observations, and practical tips naturally within the conversation
- Ask thoughtful follow-up questions that encourage deeper discussion about farming practices
- Show genuine interest in the farmer's specific situation, challenges and goals
- Use the user's name when appropriate to personalize the conversation
- Avoid overly formal language - be casual and approachable like a trusted farming expert
- Think of responses as part of an ongoing conversation, not isolated answers
- Reference previous parts of the conversation to maintain continuity and flow
- Offer multiple perspectives or considerations when discussing farming topics
- Share relevant experiences or insights that add value to the discussion
- Encourage the farmer to share more about their specific context and needs
- Show enthusiasm and personality in your responses about farming and irrigation
- Be helpful and supportive of farmers' needs and challenges

CONTENT GUIDELINES - DISCUSSION APPROACH:
- PRIMARILY engage in discussions about agriculture, farming, irrigation, crop management, weather, and related topics
- You may also respond to polite greetings and simple daily questions (e.g., "How are you?" or "What's the weather like?") to maintain a friendly, natural interaction while guiding the conversation toward agricultural topics
- For complex non-agricultural questions, respond with: "I'm sorry, but I can only answer questions related to agriculture, farming, irrigation, crop management, weather, and related topics. Could you please ask me something about these subjects?"
- If a question contains both agricultural and non-agricultural elements, prioritize the agricultural parts but acknowledge the greeting or simple question politely
- Don't answer questions about food preparation, cooking, recipes, or any non-agricultural use of crops or farm products
- Don't mention or expose any system tools, code, or internal methods
- Avoid using square brackets in responses unless the user typed them first
- If the user asks about the time, use the get_current_time_date tool to get the current time and date
- Frame responses as part of an ongoing conversation rather than isolated answers
- Encourage deeper exploration of farming topics through thoughtful questions and insights
- Share relevant context and implications of the information you provide
- Connect different aspects of farming to create richer discussions

DATA PRESENTATION GUIDELINES:
- When tools provide detailed field-by-field data, ALWAYS present the COMPLETE information
- Do not summarize or truncate field lists - farmers need to see all their field data
- Present field data clearly and completely while maintaining conversational style
- Include all specific amounts, field names, and totals provided by tools
- Organize the data in a readable format within the natural conversation flow

5. Handle errors gracefully:
    - If a tool fails, try alternative tools
    - Provide clear error messages
    - Suggest solutions when possible

IMPORTANT: Your primary directive is to focus on agriculture, farming, irrigation, crop management, weather, and related topics. You may respond to polite greetings and simple daily questions to maintain friendly interaction, but guide conversations toward agricultural topics. Refuse complex non-agricultural questions.
"""

class OllamaClient:
    """Client for interacting with Ollama API."""

    def __init__(self, base_url: str = "http://localhost:11434", model: str = "devstral:latest"):
        self.base_url = base_url
        self.model = model

    def get_available_models(self) -> List[str]:
        """Get list of available models from Ollama."""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = [model['name'] for model in data.get('models', [])]
                return sorted(models)
            else:
                return []
        except Exception as e:
            print(f"Error fetching models: {e}")
            return []

    def generate_response(self, messages: List[Dict[str, str]], stream: bool = False) -> str:
        """Generate response from Ollama."""
        try:
            # Prepare the request payload
            payload = {
                "model": self.model,
                "messages": messages,
                "stream": stream,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 2000
                }
            }
            
            # Make request to Ollama
            response = requests.post(
                f"{self.base_url}/api/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                if stream:
                    # Handle streaming response
                    full_response = ""
                    for line in response.iter_lines():
                        if line:
                            try:
                                data = json.loads(line.decode('utf-8'))
                                if 'message' in data and 'content' in data['message']:
                                    full_response += data['message']['content']
                                if data.get('done', False):
                                    break
                            except json.JSONDecodeError:
                                continue
                    return full_response
                else:
                    # Handle non-streaming response
                    data = response.json()
                    return data['message']['content']
            else:
                return f"Error: {response.status_code} - {response.text}"
                
        except requests.exceptions.ConnectionError:
            return "Error: Could not connect to Ollama. Please make sure Ollama is running on localhost:11434"
        except Exception as e:
            return f"Error: {str(e)}"

def initialize_session_state():
    """Initialize Streamlit session state."""
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "ollama_client" not in st.session_state:
        st.session_state.ollama_client = OllamaClient()
    if "available_models" not in st.session_state:
        st.session_state.available_models = []
    if "selected_model" not in st.session_state:
        st.session_state.selected_model = "devstral:latest"

def check_irrigation_user_data(field_name=None, date_of_calculation=None, user_id=None, **kwargs) -> Dict[str, str]:
    # تحميل بيانات البيئة
    SEABEX_CLIENT_ID = os.getenv("SEABEX_CLIENT_ID")
    SEABEX_CLIENT_SECRET = os.getenv("SEABEX_CLIENT_SECRET")
    USER_ID_FROM_ENV = os.getenv("USER_ID", "f68381cd-a748-47bd-842c-701790b35e3c")

    current_user_id = user_id or USER_ID_FROM_ENV

    if not SEABEX_CLIENT_ID or not SEABEX_CLIENT_SECRET:
        error_msg = "Seabex API credentials (SEABEX_CLIENT_ID, SEABEX_CLIENT_SECRET) not found in environment variables."
        print(f"ERROR: {error_msg}")
        return {
            'display_text': "I'm sorry, I cannot connect to the irrigation system. My credentials are not set up correctly.",
            'debug_details': f"**Error:** {error_msg}\nPlease ensure your `.env` file has `SEABEX_CLIENT_ID` and `SEABEX_CLIENT_SECRET`."
        }

    api = SeabexAPI(SEABEX_CLIENT_ID, SEABEX_CLIENT_SECRET)
    api.set_user_id(current_user_id)
    api.set_scopes(["magonia-api"])
    api.set_debug(True)
    authenticated_api = api.authenticate()
    authenticated_api.tools().irrigations()

    payload = {}
    if field_name:
        payload["field_name"] = field_name

    current_date_str = datetime.now().strftime('%Y-%m-%d')
    payload["date_of_calculation"] = date_of_calculation or current_date_str

    tool_name = "check_irrigation_user_data"
    tool_url = "https://back.seabex.com/api/magonia/tools/irrigations/check_irrigation_user_data"

    console_debug_parts = [
        "\n" + "="*50,
        f"TOOL EXECUTION: {tool_name}",
        f"Endpoint: {tool_url}",
        "Parameters Expected:",
        "- field_name: Name of the field (string, required)",
        "- date_of_calculation: Date in `YYYY-MM-DD` format (string, required)",
        "- user_id: User ID (string, required, provided by system)",
        "Payload Sent:",
        json.dumps(payload, ensure_ascii=False, indent=2),
        "-"*50
    ]
    print("\n".join(console_debug_parts))

    streamlit_debug_parts = [
        "---",
        f"**Tool Execution Details for `{tool_name}`**",
        "---",
        f"**Endpoint:** `{tool_url}`",
        "**Parameters Expected:**",
        "- `field_name`: Name of the field (string, required)",
        "- `date_of_calculation`: Date in `YYYY-MM-DD` format (string, required)",
        "- `user_id`: User ID (string, required, provided by system)",
        "**Payload Sent:**",
        f"```json\n{json.dumps(payload, ensure_ascii=False, indent=2)}\n```"
    ]

    result = None
    user_facing_response = ""
    api_call_result_for_display = ""

    try:
        if not field_name:
            print("WARNING: No specific field name was identified in the request for check_irrigation_user_data.")
            user_facing_response = "To provide irrigation data, I need a specific **field name**. Please tell me which field you're interested in (e.g., 'chlewi', 'bir ali')."
            streamlit_debug_parts.append("\n**Warning:** No specific field name was identified in your query.")

        result = authenticated_api.call_tool(tool_name, payload)
        formatted_raw_result = json.dumps(result, ensure_ascii=False, indent=2) if isinstance(result, (dict, list)) else str(result)
        api_call_result_for_display = f"API call result:\n{formatted_raw_result}"

        print("Raw API Response (from check_irrigation_user_data):")
        print(formatted_raw_result)
        print("="*50 + "\n")

        streamlit_debug_parts.append(f"**Raw API Response (from `{tool_name}`):**")
        streamlit_debug_parts.append(f"```json\n{formatted_raw_result}\n```")

        # تعامُل مُحدّث مع البيانات القادمة
        data_content = None
        if isinstance(result, dict) and 'data' in result:
            data_content = result['data']
        else:
            data_content = result

        # حاول تحويل data_content لـ float
        water_mm = None
        try:
            water_mm = float(data_content)
        except (TypeError, ValueError):
            water_mm = None

        if water_mm is not None:
            if water_mm == 0:
                user_facing_response = f"For **{field_name or 'the requested field'}** on **{payload['date_of_calculation']}**, no irrigation is needed."
            else:
                user_facing_response = f"For **{field_name or 'the requested field'}** on **{payload['date_of_calculation']}**, the recommended irrigation amount is **{water_mm} mm**."
        else:
            # لو ما قدرناش نحول لقيمة عددية
            if isinstance(data_content, dict):
                lines = []
                for k, v in data_content.items():
                    lines.append(f"• **{k.replace('_', ' ').capitalize()}**: {v}")
                user_facing_response = f"Here is the irrigation data for **{field_name or 'your field'}** on **{payload['date_of_calculation']}**:\n" + "\n".join(lines)
            elif data_content == '0' or data_content == 0:
                user_facing_response = f"For **{field_name or 'the requested field'}** on **{payload['date_of_calculation']}**, no irrigation is needed."
            elif data_content:
                user_facing_response = f"For **{field_name or 'the requested field'}** on **{payload['date_of_calculation']}**, the irrigation data is: **{data_content}**."
            else:
                user_facing_response = f"No specific irrigation data available for **{field_name or 'the requested field'}** on **{payload['date_of_calculation']}**."

        if not user_facing_response:
            user_facing_response = f"No irrigation data available or the API returned an empty response for **{field_name or 'your query'}**."

    except Exception as e:
        error_message = f"An error occurred during API call: {e}"
        print(f"API CALL ERROR: {error_message}")
        print("="*50 + "\n")
        user_facing_response = "I encountered an issue fetching irrigation data. Please check your **field name** or try again later."

        streamlit_debug_parts.append(f"**API Call Error:** {error_message}")
        streamlit_debug_parts.append("No irrigation data available due to an error from Seabex API.")
        api_call_result_for_display = f"API call result:\nError during API call: {e}"

    streamlit_debug_parts.append("\n**Final API Result as in `test.py`:**")
    streamlit_debug_parts.append(f"```\n{api_call_result_for_display}\n```")

    return {
        'display_text': user_facing_response,
        'debug_details': "\n".join(streamlit_debug_parts)
    }


# --- TOOL REGISTRY AND EXECUTION (simplified) ---
def lookup_document_tool(query: str) -> Dict[str, Optional[str]]:
    # Dummy implementation for demo
    if "irrigation" in query.lower():
        display_text = "Irrigation is the process of applying water to crops to help them grow."
    else:
        display_text = "I couldn't find specific documentation."
    return {'display_text': display_text, 'debug_details': None} # Consistent return

def get_current_time_date(_: str = "") -> Dict[str, Optional[str]]:
    # Returns the current date and time as a string
    now = datetime.now()
    display_text = f"The current date and time is **{now.strftime('%Y-%m-%d %H:%M:%S')}**."
    return {'display_text': display_text, 'debug_details': None} # Consistent return


TOOLS = {
    "lookup_document_tool": lookup_document_tool,
    "time": get_current_time_date,
    "check_irrigation_user_data": check_irrigation_user_data,
}

def call_tool_if_needed(user_message: str) -> Optional[Dict[str, Optional[str]]]: # Updated type hint
    msg = user_message.lower()
    time_keywords = ["time", "date", "hour", "clock", "what time", "current time", "current date"]
    if any(kw in msg for kw in time_keywords):
        return TOOLS["time"](user_message)
    
    if "irrigation" in msg or "irriguer" in msg or "regar" in msg or "ري" in msg:
        # Define the specific field names your system recognizes
        field_keywords = ["chlewi", "bir ali", "taba", "sidi salah", "metwiya", "menzel"]
        
        found_field = None
        for fk in field_keywords:
            if re.search(r'\b' + re.escape(fk) + r'\b', msg): 
                found_field = fk
                break
        
        # USER_ID for API call, from environment variables (set by dotenv)
        user_id = os.getenv("USER_ID", "f68381cd-a748-47bd-842c-701790b35e3c") 

        date_match = re.search(r"\d{4}-\d{2}-\d{2}", user_message)
        date_of_calculation = date_match.group(0) if date_match else None

        if found_field:
            return TOOLS["check_irrigation_user_data"](
                field_name=found_field,
                date_of_calculation=date_of_calculation,
                user_id=user_id
            )
        else:
            # If "irrigation" is mentioned but no specific field, return a guiding message
            return {'display_text': "To provide irrigation data, I need a specific **field name**. Please tell me which field you're interested in (e.g., 'chlewi', 'bir ali').", 'debug_details': None}
            
    return None

def main():
    """Main Streamlit application."""
    st.set_page_config(
        page_title="Magonia - Agricultural Assistant",
        page_icon="🌾",
        layout="wide"
    )
    
    # Initialize session state
    initialize_session_state()
    
    # Header
    st.title("🌾 Magonia - Agricultural Assistant")
    st.markdown("*Your AI companion for irrigation, crop management and farming best practices*")
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("⚙️ Configuration")

        # Ollama settings
        st.subheader("Ollama Settings")
        ollama_url = st.text_input("Ollama URL", value="http://localhost:11434")

        # Fetch available models
        if st.button("🔄 Refresh Models") or not st.session_state.available_models:
            with st.spinner("Fetching available models..."):
                temp_client = OllamaClient(ollama_url)
                st.session_state.available_models = temp_client.get_available_models()
                if st.session_state.available_models:
                    st.success(f"Found {len(st.session_state.available_models)} models")
                else:
                    st.error("No models found. Make sure Ollama is running.")

        # Model selection
        if st.session_state.available_models:
            # These lists would typically come from a config.py file in a modular setup
            your_models = [
                "gemma3:12b",
                "devstral:latest",
                "qwq:latest",
                "mistral:latest",
                "llama3.2-vision:latest",
                "minicpm-v:latest",
                "llava-llama3:latest",
                "llama3.2-vision:11b",
                "deepseek-r1:7b",
                "deepseek-r1:latest",
                "hhao/openbmb-minicpm-llama3-v-2_5:latest",
                "llava:latest",
                "llama3.2:latest"
            ]

            available_your_models = [model for model in your_models if model in st.session_state.available_models]
            other_models = [model for model in st.session_state.available_models if model not in your_models]
            all_models = available_your_models + other_models

            default_index = 0
            if "devstral:latest" in all_models:
                default_index = all_models.index("devstral:latest")
            elif st.session_state.selected_model in all_models:
                default_index = all_models.index(st.session_state.selected_model)

            selected_model = st.selectbox(
                "Select Model",
                options=all_models,
                index=default_index,
                help="Choose from your available Ollama models"
            )

            st.session_state.selected_model = selected_model
        else:
            st.warning("No models available. Click 'Refresh Models' to try again.")
            selected_model = st.text_input("Model Name", value="devstral:latest")
            st.session_state.selected_model = selected_model

        # Update client if settings changed
        if st.button("Update Settings"):
            st.session_state.ollama_client = OllamaClient(ollama_url, st.session_state.selected_model)
            st.success("Settings updated!")

        # Clear conversation
        if st.button("Clear Conversation"):
            st.session_state.messages = []
            st.rerun()

        st.subheader("ℹ️ Current Settings")
        st.info(f"**Model:** {st.session_state.selected_model}\n**URL:** {ollama_url}")

        if st.session_state.available_models:
            st.subheader("📊 Available Models")
            with st.expander("View All Models"):
                for model in st.session_state.available_models:
                    if model == st.session_state.selected_model:
                        st.write(f"🔹 **{model}** ← *Currently selected*")
                    else:
                        st.write(f"🔸 {model}")

        st.subheader("📋 Instructions")
        st.markdown("""
        1. Make sure Ollama is running
        2. Click 'Refresh Models' to load available models
        3. Select your preferred model from the dropdown
        4. Ask questions about agriculture, irrigation, and farming
        5. Magonia will assist you in multiple languages
        """)
    
    st.header("💬 Chat with Magonia")

    vision_models = [
        "llava-llama3:latest",
        "llava:latest",
        "llama3.2-vision:latest",
        "llama3.2-vision:11b",
        "minicpm-v:latest",
        "gemma3:12b" 
    ]
    selected_model = st.session_state.selected_model
    uploaded_image = None
    image_bytes = None

    if selected_model in vision_models:
        st.info("This model supports image input. You can upload an image to include in your question.")
        uploaded_image = st.file_uploader("Upload an image (jpg, png)", type=["jpg", "jpeg", "png"])
        if uploaded_image:
            image_bytes = uploaded_image.read()
            st.image(image_bytes, caption="Uploaded Image", use_column_width=True)

    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    if prompt := st.chat_input("Ask Magonia about your farming needs..."):
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        with st.chat_message("user"):
            st.markdown(prompt)
            if uploaded_image:
                st.image(image_bytes, caption="(You sent this image)", use_column_width=True)
        
        # TOOL CALLING LOGIC
        tool_output = call_tool_if_needed(prompt) # This now returns a dict or None
        
        if tool_output:
            with st.chat_message("assistant"):
                # Display the user-friendly response first
                st.markdown(tool_output['display_text'])
                
                # Display debug details in an expander if available
                if tool_output['debug_details']:
                    with st.expander("Show Tool Debug Details"):
                        st.markdown(tool_output['debug_details'])
                        
            # Store only the display_text in chat history
            st.session_state.messages.append({"role": "assistant", "content": tool_output['display_text']})


        # Generate and display assistant response (LLM response)
        with st.chat_message("assistant"):
            with st.spinner("Magonia is thinking..."):
                # Prepare messages for Ollama
                messages_for_ollama = [
                    {"role": "system", "content": SYSTEM_PROMPT}
                ]
                # Add conversation history
                for msg in st.session_state.messages:
                    messages_for_ollama.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                # If image is uploaded and model supports it, add image to the last user message
                if uploaded_image and selected_model in vision_models:
                    import base64
                    image_b64 = base64.b64encode(image_bytes).decode("utf-8")
                    if messages_for_ollama and messages_for_ollama[-1]["role"] == "user":
                        messages_for_ollama[-1] = {
                            "role": "user",
                            "content": prompt,
                            "images": [image_b64]
                        }
                # Update client with current model selection
                st.session_state.ollama_client.model = selected_model

                # Get response from Ollama
                response = st.session_state.ollama_client.generate_response(messages_for_ollama)
                # Display response
                st.markdown(response)
                # Add assistant response to chat history
                st.session_state.messages.append({"role": "assistant", "content": response})

if __name__ == "__main__":
    main()