#!/usr/bin/env python3
"""
Setup and run script for the Magonia Streamlit application with Ollama.
This script helps you set up and run the application easily.
"""

import subprocess
import sys
import os
import time
import requests

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required.")
        sys.exit(1)
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def install_requirements():
    """Install required packages."""
    print("📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install requirements")
        sys.exit(1)

def check_ollama():
    """Check if Ollama is running and accessible."""
    print("🔍 Checking Ollama connection...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running and accessible")
            return True
        else:
            print(f"⚠️ Ollama responded with status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Ollama at http://localhost:11434")
        print("   Please make sure Ollama is installed and running")
        return False
    except Exception as e:
        print(f"❌ Error checking Ollama: {e}")
        return False

def check_available_models():
    """Check what models are available in Ollama."""
    print("🔍 Checking available models...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json()
            model_names = [model['name'] for model in models.get('models', [])]

            if model_names:
                print(f"✅ Found {len(model_names)} model(s):")
                for model in sorted(model_names):
                    print(f"   📦 {model}")

                # Check for devstral specifically
                devstral_models = [name for name in model_names if 'devstral' in name.lower()]
                if devstral_models:
                    print(f"✅ devstral model available: {', '.join(devstral_models)}")
                else:
                    print("ℹ️ devstral model not found (you can install it with: ollama pull devstral)")

                return True, model_names
            else:
                print("⚠️ No models found")
                print("   You can install models with: ollama pull <model-name>")
                return False, []
        else:
            print("❌ Could not retrieve model list from Ollama")
            return False, []
    except Exception as e:
        print(f"❌ Error checking available models: {e}")
        return False, []

def run_streamlit():
    """Run the Streamlit application."""
    print("🚀 Starting Streamlit application...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "streamlit_ollama_app.py"])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running Streamlit: {e}")

def main():
    """Main setup and run function."""
    print("🌾 Magonia - Agricultural Assistant Setup")
    print("=" * 50)
    
    # Check Python version
    check_python_version()
    
    # Install requirements
    install_requirements()
    
    # Check Ollama
    ollama_running = check_ollama()
    
    if ollama_running:
        # Check available models
        models_available, model_list = check_available_models()

        if not models_available:
            print("\n⚠️ No models are available in Ollama.")
            print("   You can install models with commands like:")
            print("   - ollama pull devstral")
            print("   - ollama pull gemma3:12b")
            print("   - ollama pull mistral")

            choice = input("\nDo you want to continue anyway? (y/n): ").lower().strip()
            if choice != 'y':
                print("👋 Setup cancelled. Please install some models and try again.")
                sys.exit(0)
    else:
        print("\n⚠️ Ollama is not running or not accessible.")
        print("   Please:")
        print("   1. Install Ollama from https://ollama.ai")
        print("   2. Start Ollama service")
        print("   3. Install models like: ollama pull devstral")
        print("   4. Or install other models: ollama pull gemma3:12b")
        
        choice = input("\nDo you want to continue anyway? (y/n): ").lower().strip()
        if choice != 'y':
            print("👋 Setup cancelled. Please set up Ollama and try again.")
            sys.exit(0)
    
    print("\n" + "=" * 50)
    print("🎉 Setup complete! Starting the application...")
    print("   The app will open in your browser automatically.")
    print("   Press Ctrl+C to stop the application.")
    print("=" * 50)
    
    # Small delay before starting
    time.sleep(2)
    
    # Run Streamlit
    run_streamlit()

if __name__ == "__main__":
    main()
