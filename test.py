from seabex_api import SeabexAPI
import os
import requests
import json
from dotenv import load_dotenv
import colorama
from colorama import Fore, Style
import traceback
import sys
from datetime import datetime, timedelta
import time

load_dotenv()
token = os.getenv('AUTH_TOKEN')

# Set your credentials and parameters
SEABEX_CLIENT_ID = os.getenv("SEABEX_CLIENT_ID", "3")
SEABEX_CLIENT_SECRET = os.getenv("SEABEX_CLIENT_SECRET", "JrTtI5IDjiZr6HCnDAPoHRbDm1v71zV9jGEYTmEF")
USER_ID = "f68381cd-a748-47bd-842c-701790b35e3c"

# Use the correct scope for your client_id
api = SeabexAPI(SEABEX_CLIENT_ID, SEABEX_CLIENT_SECRET)
api.set_scopes(["magonia-api"])
api.set_user_id(USER_ID)
api.authenticate()

# Prepare and call the tool
FIELD_NAME = "chlewi"  # Change as needed
DATE_OF_CALCULATION = "2024-03-15"  # Change as needed
api.tools().irrigations()
payload = {
    "field_name": FIELD_NAME,
    "date_of_calculation": DATE_OF_CALCULATION
}
result = api.call_tool("check_irrigation_user_data", payload)

print("API call result:")
print(result)

def send_prompt(prompt, session_id="session123", user_id="f68381cd-a748-47bd-842c-701790b35e3c", token=None):
    url = "http://127.0.0.1:8080/magonia/chat"
    data = {
        "session_id": session_id,
        "prompt": prompt,
        "chat_history": "",
        "scopes": ["magonia-api"],
        "user_id": user_id
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    print("Status Code:", response.status_code)

    if response.status_code != 200:
        print("Response Text:", response.text)
        try:
            return response.text
        except Exception as e:
            print(e)
            traceback.print_exc()
            return {
                'error': 'An unexpected error occurred, and the response is not valid JSON.',
                'status_code': response.status_code
            }

    try:
        json_response = response.json()
        print("\033[33m" + "Response: " + str(json_response) + "\033[0m")
        return json_response
    except Exception as e:
        print(e)
        traceback.print_exc()
        return None

