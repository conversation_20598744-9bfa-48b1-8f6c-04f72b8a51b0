#!/usr/bin/env python3
"""
Simple voice test script to verify voice capabilities work
"""

import os
import time
try:
    import speech_recognition as sr
    import pyttsx3
    from gtts import gTTS
    import pygame
    print("✅ All voice libraries imported successfully!")
except ImportError as e:
    print(f"❌ Import error: {e}")
    exit(1)

def test_text_to_speech():
    """Test text-to-speech functionality"""
    print("\n🔊 Testing Text-to-Speech...")
    
    try:
        # Test pyttsx3 (offline TTS)
        print("Testing pyttsx3 (offline)...")
        engine = pyttsx3.init()
        engine.setProperty('rate', 150)
        engine.setProperty('volume', 0.9)
        engine.say("Hello! This is a test of the offline text to speech system.")
        engine.runAndWait()
        print("✅ pyttsx3 test completed")
        
        # Test gTTS (online TTS)
        print("Testing gTTS (online)...")
        tts = gTTS(text="Hello! This is a test of the Google text to speech system.", lang='en', slow=False)
        tts.save("test_speech.mp3")
        
        # Play with pygame
        pygame.mixer.init()
        pygame.mixer.music.load("test_speech.mp3")
        pygame.mixer.music.play()
        
        while pygame.mixer.music.get_busy():
            time.sleep(0.1)
            
        # Clean up
        os.remove("test_speech.mp3")
        print("✅ gTTS test completed")
        
    except Exception as e:
        print(f"❌ TTS test failed: {e}")

def test_speech_recognition():
    """Test speech recognition functionality"""
    print("\n🎤 Testing Speech Recognition...")
    
    try:
        recognizer = sr.Recognizer()
        microphone = sr.Microphone()
        
        print("Available microphones:")
        for i, mic_name in enumerate(sr.Microphone.list_microphone_names()):
            print(f"  {i}: {mic_name}")
        
        # Adjust for ambient noise
        print("Adjusting for ambient noise...")
        with microphone as source:
            recognizer.adjust_for_ambient_noise(source, duration=1)
        
        print("✅ Speech recognition setup completed")
        print("Note: Actual speech recognition test requires manual interaction")
        
    except Exception as e:
        print(f"❌ Speech recognition test failed: {e}")

def main():
    print("🎙️ Voice Capabilities Test")
    print("=" * 40)
    
    test_text_to_speech()
    test_speech_recognition()
    
    print("\n🎉 Voice test completed!")
    print("If you heard the speech and saw no errors, voice features should work in the main app.")

if __name__ == "__main__":
    main()
