import os
import sys
import json
import re
import traceback
import requests
from datetime import datetime
from typing import List, Dict, Optional
from dotenv import load_dotenv
from seabex_api import SeabexAPI

# Load environment variables
load_dotenv()

# Seabex credentials
SEABEX_CLIENT_ID = os.getenv("SEABEX_CLIENT_ID")
SEABEX_CLIENT_SECRET = os.getenv("SEABEX_CLIENT_SECRET")
SEABEX_USER_ID = os.getenv("USER_ID", "f68381cd-a748-47bd-842c-701790b35e3c")

# Ollama settings
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_MODEL = "qwen3:14b"

# Known field names
FIELD_KEYWORDS = ["chlewi", "bir ali", "taba", "sidi salah", "metwiya", "menzel"]

# ---------- Ollama Client ----------
class OllamaClient:
    def __init__(self, base_url: str, model: str):
        self.base_url = base_url
        self.model = model

    def get_available_models(self) -> List[str]:
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                return [model['name'] for model in response.json().get('models', [])]
            return []
        except:
            return []

    def generate_response(self, messages: List[Dict[str, str]], stream: bool = False) -> str:
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": stream,
            "options": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 500}
        }
        try:
            response = requests.post(f"{self.base_url}/api/chat", json=payload)
            response.raise_for_status()
            return response.json()['message']['content']
        except Exception as e:
            return f"Error during Ollama call: {str(e)}"


# ---------- Seabex Tool Call ----------
def get_water_amount_to_irrigate(field_name: str, date_of_calculation: str, user_id: str) -> Optional[float]:
    try:
        api = SeabexAPI(SEABEX_CLIENT_ID, SEABEX_CLIENT_SECRET)
        api.set_user_id(user_id)
        api.set_scopes(["magonia-api"])
        api.authenticate()

        api.tools().irrigations()

        cache_buster = datetime.now().isoformat() + "_prxakslp"
        payload = {
            "user_id": user_id,
            "field_name": field_name,
            "date_of_calculation": date_of_calculation,
            "_cache_buster": cache_buster
        }

        print(f"🔗 Calling API: check_irrigation_user_data\n📦 Payload: {payload}")
        result = api.call_tool("check_irrigation_user_data", payload)
        print(f"✅ API Response: {result}")

        # Handle case where result is a dict
        if isinstance(result, dict):
            data = result.get("data")
        else:
            data = result  # If it's already a string like "1.5"

        # Try converting to float directly
        try:
            water_mm = float(data)
            if water_mm == 0:
                print("💧 No irrigation is needed.")
                return 0.0
            print(f"💧 Irrigation needed: {water_mm} mm")
            return water_mm
        except (TypeError, ValueError):
            print("⚠️ Data could not be converted to float.")
            return None

    except Exception as e:
        traceback.print_exc()
        return None



# ---------- Core Logic ----------
def process_user_query_with_tools(query: str, ollama_client: OllamaClient) -> str:
    query_lower = query.lower()
    tool_used = False
    tool_output = None

    if any(term in query_lower for term in ["irrigate", "irrigation", "ري"]):
        found_field = next((f for f in FIELD_KEYWORDS if re.search(rf'\b{re.escape(f)}\b', query_lower)), None)
        date_match = re.search(r"\d{4}-\d{2}-\d{2}", query)
        date = date_match.group(0) if date_match else datetime.now().strftime('%Y-%m-%d')

        if found_field:
            print(f"📍 Field: {found_field} | 📅 Date: {date}")
            water_amount = get_water_amount_to_irrigate(found_field, date, SEABEX_USER_ID)
            tool_used = True

            if water_amount is None:
                tool_output = f"Irrigation is recommended for your field **{found_field.capitalize()}** on **{date}**, but the system did not provide a specific water amount."
            elif water_amount == 0:
                tool_output = f"No irrigation is needed for your field **{found_field.capitalize()}** on **{date}**."
            else:
                tool_output = f"Yes, irrigation is recommended for your field **{found_field.capitalize()}** on **{date}**. The recommended amount of water is **{water_amount} mm**."

            llm_prompt = f"The user asked: '{query}'.\nThe result from the irrigation tool is:\n{tool_output}\nPlease answer clearly."
        else:
            llm_prompt = f"The user asked: '{query}', but no valid field name was found. Please prompt the user to specify a field like 'chlewi'."
            tool_output = "No field detected."

    else:
        llm_prompt = f"The user asked: '{query}'. Provide a helpful and concise response."

    messages = [
        {"role": "system", "content": "You are Magonia, a helpful smart farming assistant."},
        {"role": "user", "content": llm_prompt}
    ]

    print("\n🧠 Sending to Ollama:\n", messages[-1]['content'])
    return ollama_client.generate_response(messages)


# ---------- Entry Point ----------
if __name__ == "__main__":
    print("🌿 Starting Magonia Smart Irrigation Tool")

    # Init Ollama
    ollama = OllamaClient(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL)

    # Check model availability
    if OLLAMA_MODEL not in ollama.get_available_models():
        print(f"❌ Model '{OLLAMA_MODEL}' not found in Ollama. Please run:\n   ollama pull {OLLAMA_MODEL}")
        sys.exit(1)

    # Test question
    user_question = "Do I have to irrigate my field CHlewi on 2024-02-21?"
    print(f"\n❓ User Question: {user_question}")

    final_response = process_user_query_with_tools(user_question, ollama)

    print("\n✅ Final Answer to User:\n", final_response)
    print("\n🌾 End of Session")
